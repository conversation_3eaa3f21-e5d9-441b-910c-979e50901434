import { Injectable, NgZone, inject } from '@angular/core';
import { environment } from '../../environments/environment';
import { BehaviorSubject, Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { initializeApp } from 'firebase/app';
import {
    getMessaging,
    getToken,
    onMessage,
    Messaging,
    MessagePayload,
} from 'firebase/messaging';
import { Router } from '@angular/router';

export interface NotificationPayload {
    title: string;
    body: string;
    icon?: string;
    data?: any;
    clickAction?: string;
}

@Injectable({
    providedIn: 'root',
})
export class FcmService {
    private messaging: Messaging | null = null;
    private messageSource = new BehaviorSubject<MessagePayload | null>(null);
    private tokenSource = new BehaviorSubject<string | null>(null);
    private permissionSource = new BehaviorSubject<NotificationPermission>(
        'default',
    );

    currentMessage$ = this.messageSource.asObservable();
    currentToken$ = this.tokenSource.asObservable();
    currentPermission$ = this.permissionSource.asObservable();

    private http = inject(HttpClient);
    private zone = inject(NgZone);
    private router = inject(Router);

    constructor() {
        try {
            if (this.isSupported()) {
                const app = initializeApp(environment.firebase);
                this.messaging = getMessaging(app);
                this.setupMessaging();
                this.checkPermission();
            }
        } catch (error) {
            console.error('Error initializing Firebase:', error);
        }
    }

    /**
     * Check if FCM is supported in this browser
     */
    isSupported(): boolean {
        return (
            'Notification' in window &&
            'serviceWorker' in navigator &&
            'PushManager' in window
        );
    }

    /**
     * Request permission for notifications
     */
    async requestPermission(): Promise<boolean> {
        if (!this.isSupported()) {
            console.warn('Notifications are not supported in this browser');
            return false;
        }

        try {
            const permission = await Notification.requestPermission();
            this.permissionSource.next(permission);

            if (permission === 'granted') {
                await this.getToken();
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error requesting notification permission:', error);
            return false;
        }
    }

    /**
     * Get the FCM token for the current user
     */
    async getToken(): Promise<string | null> {
        if (!this.messaging || !this.isSupported()) {
            return null;
        }

        try {
            if (Notification.permission !== 'granted') {
                return null;
            }

            const token = await getToken(this.messaging, {
                vapidKey: environment.firebase.vapidKey,
            });

            if (token) {
                this.tokenSource.next(token);
                this.saveTokenToLocalStorage(token);
                return token;
            } else {
                console.warn('No registration token available');
                return null;
            }
        } catch (error) {
            console.error('Error getting token:', error);
            return null;
        }
    }

    /**
     * Save the FCM token to local storage
     * Since we're not modifying the backend, we'll store it locally
     */
    private saveTokenToLocalStorage(token: string): void {
        localStorage.setItem('fcm_token', token);
        console.log('FCM token saved to local storage:', token);
    }

    /**
     * Set up message handling
     */
    private setupMessaging(): void {
        if (!this.messaging) return;

        onMessage(this.messaging, (payload: any) => {
            this.zone.run(() => {
                console.log('Message received:', payload);
                this.messageSource.next(payload);
                this.showNotification(payload);
            });
        });
    }

    /**
     * Show a notification
     */
    private showNotification(payload: MessagePayload): void {
        if (!payload.notification) return;

        const { title, body } = payload.notification;
        const icon =
            payload.notification.icon || '/assets/icons/icon-128x128.png';

        if (Notification.permission === 'granted') {
            const notification = new Notification(title || 'Notification', {
                body: body || '',
                icon,
            });

            notification.onclick = () => {
                this.zone.run(() => {
                    // if (payload.data && payload.data.clickAction) {
                    //     this.router.navigateByUrl(payload.data.clickAction);
                    // }
                    window.focus();
                    notification.close();
                });
            };
        }
    }

    /**
     * Check notification permission
     */
    private checkPermission(): void {
        if (this.isSupported()) {
            this.permissionSource.next(Notification.permission);
        }
    }

    /**
     * Send a test notification (for development purposes)
     */
    sendTestNotification(payload: NotificationPayload): void {
        if (!this.isSupported()) {
            console.warn('Notifications are not supported in this browser');
            return;
        }

        // Create a notification directly since we're not modifying the backend
        if (Notification.permission === 'granted') {
            const notification = new Notification(payload.title, {
                body: payload.body,
                icon: payload.icon || '/assets/icons/icon-128x128.png',
                data: payload.data,
            });

            notification.onclick = () => {
                this.zone.run(() => {
                    if (payload.clickAction) {
                        this.router.navigateByUrl(payload.clickAction);
                    }
                    window.focus();
                    notification.close();
                });
            };
        }
    }
}
