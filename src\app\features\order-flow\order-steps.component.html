<div class="uber-flow-container">
    <!-- Pending Orders Dialog -->
    <p-dialog
        header="You have a trip in progress"
        [modal]="true"
        [visible]="showPendingOrderDialog()"
        [closable]="false"
        [style]="{ width: '90vw', maxWidth: '400px' }"
        styleClass="!bg-white"
    >
        <div class="py-4 text-center">
            <i class="pi pi-car mb-4 text-4xl text-black"></i>
            <p class="mb-6 text-base text-gray-800">
                You have an active trip. Would you like to continue tracking it
                or start a new trip?
            </p>

            @if (pendingOrders().length > 0) {
                <div class="mb-6 rounded-xl bg-gray-50 p-4">
                    <div class="flex items-center justify-between">
                        <span class="font-medium"
                            >Trip #{{ pendingOrders()[0].id.slice(-8) }}</span
                        >
                        <p-tag
                            [value]="pendingOrders()[0].status"
                            severity="info"
                            styleClass="uber-tag"
                        >
                        </p-tag>
                    </div>
                </div>
            }
        </div>

        <ng-template pTemplate="footer">
            <div class="flex w-full flex-col gap-3">
                <button
                    class="w-full rounded-md bg-black p-2 text-white"
                    (click)="handlePendingOrder(true)"
                >
                    Continue Trip
                </button>
                <button
                    class="w-full rounded-md p-2"
                    (click)="handlePendingOrder(false)"
                >
                    Start New Trip
                </button>
            </div>
        </ng-template>
    </p-dialog>

    <!-- Step Content -->
    <div class="uber-content">
        <!-- Step 1: Checking Pending Orders -->
        @if (currentStep() === OrderStep.CHECK_PENDING) {
            <div class="uber-loading-screen">
                @if (isLoading()) {
                    <div class="uber-spinner"></div>
                    <h2 class="uber-loading-text">Setting up your ride...</h2>
                } @else {
                    <i class="pi pi-check-circle mb-4 text-5xl text-black"></i>
                    <h2 class="uber-loading-text">Ready to go!</h2>
                }
            </div>
        }

        <!-- Step 2: Select Pickup Location -->
        @if (currentStep() === OrderStep.SELECT_PICKUP) {
            <div class="uber-location-step">
                <div class="uber-location-header">
                    <div class="uber-location-icon pickup"></div>
                    <div>
                        <h3 class="uber-location-label">
                            Pick your pickup location
                        </h3>
                    </div>
                </div>

                <!-- Marked Places Horizontal Scroll -->
                @if (markedPlaces().length > 0) {
                    <div class="uber-marked-places">
                        <div class="uber-marked-places-scroll">
                            @for (place of markedPlaces(); track place.id) {
                                <div
                                    class="uber-marked-place-card"
                                    (click)="selectMarkedPlace(place)"
                                >
                                    <span class="uber-marked-place-name">{{
                                        place.name
                                    }}</span>
                                </div>
                            }
                        </div>
                    </div>
                }

                <div class="uber-location-picker">
                    <app-location-picker
                        title="Pickup"
                        placeholder="Enter pickup location"
                        (locationSelected)="onPickupSelected($event)"
                    >
                    </app-location-picker>
                </div>
            </div>
        }

        <!-- Step 3: Select Dropoff Location -->
        @if (currentStep() === OrderStep.SELECT_DROPOFF) {
            <div class="uber-location-step">
                <!-- Pickup Confirmation -->
                <div class="uber-location-confirmed">
                    <div class="uber-location-icon pickup confirmed"></div>
                    <div class="uber-location-details">
                        <h4 class="uber-location-type">Pickup</h4>
                        <p class="uber-location-address">
                            {{
                                pickupAddress() ||
                                    pickupLocation()!.lat.toFixed(4) +
                                        ", " +
                                        pickupLocation()!.lng.toFixed(4)
                            }}
                        </p>
                    </div>
                </div>

                <!-- Dropoff Selection -->
                <div class="uber-location-header">
                    <div class="uber-location-icon dropoff"></div>
                    <div>
                        <h3 class="uber-location-label">Where to?</h3>
                    </div>
                </div>

                <!-- Marked Places Horizontal Scroll for Dropoff -->
                @if (markedPlaces().length > 0) {
                    <div class="uber-marked-places">
                        <div class="uber-marked-places-scroll">
                            @for (place of markedPlaces(); track place.id) {
                                <div
                                    class="uber-marked-place-card"
                                    (click)="selectMarkedPlaceAsDropoff(place)"
                                >
                                    <span class="uber-marked-place-name">{{
                                        place.name
                                    }}</span>
                                </div>
                            }
                        </div>
                    </div>
                }

                <div class="uber-location-picker">
                    <app-location-picker
                        title="Destination"
                        placeholder="Enter destination"
                        (locationSelected)="onDropoffSelected($event)"
                    >
                    </app-location-picker>
                </div>
            </div>
        }

        <!-- Step 4: Calculating Price -->
        @if (currentStep() === OrderStep.CALCULATE_PRICE) {
            <div class="uber-loading-screen">
                <div class="uber-spinner"></div>
                <h2 class="uber-loading-text">Finding your price...</h2>
                <p class="uber-loading-subtitle">
                    We're calculating the best price based on distance and
                    demand
                </p>
            </div>
        }

        <!-- Step 5: Confirm Order -->
        @if (currentStep() === OrderStep.CONFIRM_ORDER) {
            <div class="uber-confirmation">
                <!-- Price Card - Moved to Top -->
                <div class="uber-price-card">
                    <div class="uber-car-option">
                        <div class="uber-car-info">
                            <div class="uber-car-icon">
                                <i class="pi pi-car"></i>
                            </div>
                            <div class="uber-car-details">
                                @if (estimatedDurationMinutes()) {
                                    <p class="uber-trip-duration">
                                        {{ estimatedDurationMinutes() }} min
                                    </p>
                                }
                                @if (estimatedDistanceKm()) {
                                    <p class="uber-car-distance">
                                        {{ estimatedDistanceKm() }} km
                                    </p>
                                }
                            </div>
                        </div>
                        <div class="uber-car-price">
                            <span class="uber-price"
                                >${{ calculatedPrice()?.toFixed(2) }}</span
                            >
                        </div>
                    </div>
                </div>

                <!-- Route Container - Full Space Between Price Card and Buttons -->
                <div class="uber-route-container">
                    <!-- From Section - At Top -->
                    <div class="uber-location-section from-section">
                        <div class="uber-location-item">
                            <div class="uber-location-icon pickup"></div>
                            <div class="uber-location-details">
                                <h4 class="uber-location-label">From</h4>
                                <div class="uber-location-address-detailed">
                                    <p class="uber-location-address-primary">
                                        {{ getDetailedPickupAddress().primary }}
                                    </p>
                                    @if (getDetailedPickupAddress().secondary) {
                                        <p
                                            class="uber-location-address-secondary"
                                        >
                                            {{
                                                getDetailedPickupAddress()
                                                    .secondary
                                            }}
                                        </p>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Dynamic Connecting Line -->
                    <div class="uber-route-connector"></div>

                    <!-- To Section - At Bottom -->
                    <div class="uber-location-section to-section">
                        <div class="uber-location-item">
                            <div class="uber-location-icon dropoff"></div>
                            <div class="uber-location-details">
                                <h4 class="uber-location-label">To</h4>
                                <div class="uber-location-address-detailed">
                                    <p class="uber-location-address-primary">
                                        {{
                                            getDetailedDropoffAddress().primary
                                        }}
                                    </p>
                                    @if (
                                        getDetailedDropoffAddress().secondary
                                    ) {
                                        <p
                                            class="uber-location-address-secondary"
                                        >
                                            {{
                                                getDetailedDropoffAddress()
                                                    .secondary
                                            }}
                                        </p>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Confirm Buttons -->
                <div class="uber-confirm-section">
                    <button
                        class="uber-button uber-button-primary"
                        [disabled]="isLoading()"
                        (click)="confirmOrder()"
                    >
                        @if (isLoading()) {
                            Requesting...
                        } @else {
                            Confirm Order
                        }
                    </button>
                    <button
                        class="uber-button uber-button-secondary"
                        (click)="declineOrder()"
                    >
                        Cancel
                    </button>
                </div>
            </div>
        }

        <!-- Step 6: Looking for Driver -->
        @if (currentStep() === OrderStep.ORDER_CREATED) {
            <div class="uber-driver-search">
                <div class="uber-driver-search-content">
                    <!-- Animated car icon -->
                    <div class="uber-car-animation">
                        <div class="uber-car-icon-large">
                            <i class="pi pi-car"></i>
                        </div>
                        <div class="uber-search-waves">
                            <div class="uber-wave wave-1"></div>
                            <div class="uber-wave wave-2"></div>
                            <div class="uber-wave wave-3"></div>
                        </div>
                    </div>

                    <!-- Main message -->
                    <div class="uber-search-message">
                        <h2 class="uber-search-title">Finding your driver</h2>
                        <p class="uber-search-subtitle">
                            We're connecting you with a nearby driver
                        </p>
                    </div>

                    <!-- Trip details card -->
                    <div class="uber-trip-summary">
                        <div class="uber-trip-route">
                            <div class="uber-route-item">
                                <div class="uber-route-dot pickup"></div>
                                <div class="uber-route-info">
                                    <span class="uber-route-label">From</span>
                                    <span class="uber-route-address">{{
                                        getDetailedPickupAddress().primary
                                    }}</span>
                                </div>
                            </div>
                            <div class="uber-route-line"></div>
                            <div class="uber-route-item">
                                <div class="uber-route-dot dropoff"></div>
                                <div class="uber-route-info">
                                    <span class="uber-route-label">To</span>
                                    <span class="uber-route-address">{{
                                        getDetailedDropoffAddress().primary
                                    }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="uber-trip-price">
                            <span class="uber-price-label">Trip cost</span>
                            <span class="uber-price-value"
                                >${{ calculatedPrice()?.toFixed(2) }}</span
                            >
                        </div>
                    </div>

                    <!-- Cancel button -->
                    <div class="uber-cancel-section">
                        <button
                            class="uber-button uber-button-cancel"
                            (click)="cancelOrderSearch()"
                        >
                            Cancel Trip
                        </button>
                    </div>
                </div>
            </div>
        }
    </div>
</div>
