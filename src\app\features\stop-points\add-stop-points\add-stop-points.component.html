<div class="p-4">
    <p-card header="Add Stop Point">
        <form [formGroup]="placeForm" class="mb-4 flex flex-col gap-4">
            <div class="field">
                <label for="name" class="mb-2 block text-sm font-medium"
                    >Name</label
                >
                <input
                    id="name"
                    type="text"
                    pInputText
                    formControlName="name"
                    class="w-full"
                    placeholder="Enter place name"
                />
            </div>
        </form>

        <div class="mb-2 flex gap-2 px-2">
            <p-inputGroup>
                <p-inputGroupAddon>
                    <i class="pi pi-search"></i>
                </p-inputGroupAddon>
                <p-autoComplete
                    class="w-full"
                    styleClass="!w-full"
                    [ngModel]="search()"
                    [ngModelOptions]="{ standalone: true }"
                    (completeMethod)="search.set($event.query)"
                    [suggestions]="searchRes$() || []"
                    [optionLabel]="'display_name'"
                    [optionValue]="'latlng'"
                    (onSelect)="afterSearch($event.value)"
                    [appendTo]="'body'"
                ></p-autoComplete>
            </p-inputGroup>
            <button
                class="flex-shrink-0"
                pButton
                (click)="pointGeoLocation()"
                [pTooltip]="'point location'"
            >
                <i class="pi pi-map-marker"></i>
            </button>
        </div>

        <div class="field">
            <label class="mb-2 block text-sm font-medium">Location</label>
            <div class="h-[300px] w-full">
                <app-map
                    [options]="options()"
                    [nodes]="mapMarker()"
                    (clickMap)="handleMapClick($event)"
                ></app-map>
            </div>
            <small class="mt-2 block text-muted-color">
                Click on the map to select a location or search above
            </small>
        </div>

        <div class="mt-4 flex justify-end gap-2">
            <button
                pButton
                type="button"
                label="Cancel"
                class="p-button-outlined"
                [routerLink]="['/main', 'stop-points']"
            ></button>
            <button
                pButton
                type="submit"
                label="Save"
                [disabled]="placeForm.invalid || !selectedLocation()"
                (click)="savePlace()"
            ></button>
        </div>
    </p-card>
</div>
