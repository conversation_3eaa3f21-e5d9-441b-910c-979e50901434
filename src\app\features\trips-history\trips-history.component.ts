import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { ToastModule } from 'primeng/toast';

import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { MessageService } from 'primeng/api';
import { Order, OrderService, OrderStatus } from '../../services/order.service';
import { TripMapDialogComponent } from '../../shared/components/trip-map-dialog/trip-map-dialog.component';
import { interval, Subscription } from 'rxjs';

@Component({
    selector: 'app-trips-history',
    standalone: true,
    imports: [
        CommonModule,
        CardModule,
        ButtonModule,
        ToastModule,
    ],
    templateUrl: './trips-history.component.html',
})
export class TripsHistoryComponent implements OnInit {
    private orderService = inject(OrderService);
    private dialogService = inject(DialogService);
    private messageService = inject(MessageService);
    private router = inject(Router);

    orders: Order[] = [];
    private dialogRef: DynamicDialogRef | undefined;

    ngOnInit(): void {
        this.loadOrders();
    }

    loadOrders(): void {
        this.orderService.getUserOrders().subscribe((response) => {
            if (response.data) {
                this.orders = response.data;
            }
        });
    }

    viewTripDetails(order: Order): void {
        if (order.tripId) {
            // Navigate to the new trip info page
            this.router.navigate(['/main/trip', order.tripId]);
        } else {
            this.messageService.add({
                severity: 'warn',
                summary: 'No Trip',
                detail: 'This order does not have an associated trip yet.'
            });
        }
    }

    formatDate(dateString: string): string {
        const date = new Date(dateString);
        return date.toLocaleString();
    }

    /**
     * Get CSS class for order status styling
     * @param status Order status
     * @returns CSS class string
     */
    getStatusClass(status: OrderStatus): string {
        switch (status) {
            case OrderStatus.PENDING:
                return 'bg-blue-100 text-blue-800';
            case OrderStatus.SUGGESTED_FOR_DRIVER:
                return 'bg-yellow-100 text-yellow-800';
            case OrderStatus.CONFIRMED:
                return 'bg-green-100 text-green-800';
            case OrderStatus.COMPLETED:
                return 'bg-purple-100 text-purple-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }

    /**
     * Approve an order (for drivers)
     * @param order The order to approve
     */
    approve(order: Order): void {
        // Check if the order can be approved
        if (
            order.status !== OrderStatus.PENDING &&
            order.status !== OrderStatus.SUGGESTED_FOR_DRIVER
        ) {
            this.messageService.add({
                severity: 'warn',
                summary: 'Cannot Approve',
                detail: 'This order cannot be approved in its current status.',
            });
            return;
        }

        this.orderService.approveOrder(order.id).subscribe({
            next: (response) => {
                if (response.data) {
                    
                    // Check if the response already contains the tripId
                    if (response.data.tripId) {
                        // Navigate immediately to trip info page
                        this.router.navigate(['/main/trip', response.data.tripId]);
                    } else {
                        // Fallback: Start polling for trip ID if not immediately available
                        this.pollForTripId(order.id);
                    }
                }
            },
            error: (error) => {
                console.error('Error approving order:', error);
                this.messageService.add({
                    severity: 'error',
                    summary: 'Approval Failed',
                    detail: 'Failed to approve the trip. Please try again.',
                });
            },
        });
    }

    private pollForTripId(orderId: string): void {
        let pollCount = 0;
        const maxPolls = 10; // Maximum 30 seconds of polling (3 seconds * 10)
        
        const pollSubscription = interval(3000).subscribe(() => {
            pollCount++;
            
            this.orderService.getUserOrders().subscribe({
                next: (response) => {
                    if (response.data) {
                        const order = response.data.find(o => o.id === orderId);
                        if (order && order.tripId) {
                            // Found the trip ID, navigate to trip info
                            pollSubscription.unsubscribe();
                            this.router.navigate(['/main/trip', order.tripId]);
                        } else if (pollCount >= maxPolls) {
                            // Stop polling after max attempts
                            pollSubscription.unsubscribe();
                            // Refresh the orders list as fallback
                            this.loadOrders();
                        }
                    }
                },
                error: (error) => {
                    console.error('Error polling for trip ID:', error);
                    if (pollCount >= maxPolls) {
                        pollSubscription.unsubscribe();
                        this.loadOrders();
                    }
                }
            });
        });
    }

    ngOnDestroy(): void {
        if (this.dialogRef) {
            this.dialogRef.close();
        }
    }
}
