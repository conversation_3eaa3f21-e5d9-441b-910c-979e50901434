<div class="relative h-full w-full bg-background-color-100 lg:flex">
    <!-- Map Section -->
    <div class="h-[60vh] flex-grow lg:h-[88dvh]">
        <app-map class="size-full" [nodes]="markedPlacesMap()"></app-map>

        <!-- Floating Add Button -->
        <div class="absolute left-4 top-4 z-[400]">
            <button
                class="flex items-center gap-2 rounded-lg bg-main-color-600 px-4 py-3 text-background-color-100 shadow-shadow-300 transition-all duration-200 hover:-translate-y-0.5 hover:bg-main-color-700 hover:shadow-shadow-400 focus:outline-none focus:ring-2 focus:ring-main-color-600 focus:ring-offset-2"
                routerLink="add"
            >
                <i class="pi pi-plus text-sm"></i>
                <span class="font-medium">Add Place</span>
            </button>
        </div>

        <!-- Places Count Badge -->
        @if (markedPlaces().length > 0) {
            <div class="absolute right-4 top-4 z-[400]">
                <div
                    class="rounded-full border border-background-color-300 bg-background-color-100 px-3 py-1 shadow-shadow-200"
                >
                    <span class="text-sm font-medium text-text-color-100">
                        {{ markedPlaces().length }}
                        {{ markedPlaces().length === 1 ? "Place" : "Places" }}
                    </span>
                </div>
            </div>
        }
    </div>
    <!-- Places List Section -->
    <div
        class="h-[40vh] w-full overflow-y-auto bg-background-color-100 lg:h-[88dvh] lg:w-80 lg:border-l lg:border-background-color-300"
    >
        <!-- Header -->
        <div
            class="sticky top-0 z-10 border-b border-background-color-300 bg-background-color-100 p-4"
        >
            <h2 class="text-lg font-semibold text-text-color-100">
                Marked Places
            </h2>
            @if (markedPlaces().length === 0) {
                <p class="mt-1 text-sm text-text-color-300">
                    No places marked yet
                </p>
            }
        </div>

        <!-- Places List -->
        <div class="space-y-4 p-4">
            @if (markedPlaces().length === 0) {
                <!-- Empty State -->
                <div
                    class="flex flex-col items-center justify-center py-12 text-center"
                >
                    <div
                        class="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-background-color-200"
                    >
                        <i
                            class="pi pi-map-marker text-2xl text-text-color-300"
                        ></i>
                    </div>
                    <h3 class="mb-2 text-lg font-medium text-text-color-200">
                        No marked places
                    </h3>
                    <p class="mb-4 max-w-xs text-sm text-text-color-300">
                        Start by adding your first marked place to the map
                    </p>
                    <button
                        class="flex items-center gap-2 rounded-lg bg-main-color-600 px-4 py-2 text-sm font-medium text-background-color-100 transition-all duration-200 hover:bg-main-color-700"
                        routerLink="add"
                    >
                        <i class="pi pi-plus text-xs"></i>
                        Add First Place
                    </button>
                </div>
            } @else {
                @for (place of markedPlaces(); track $index) {
                    <div
                        class="group rounded-xl border border-background-color-300 bg-background-color-100 p-4 shadow-shadow-200 transition-all duration-200 hover:border-main-color-600 hover:shadow-shadow-400"
                    >
                        <div class="flex items-start justify-between">
                            <div class="min-w-0 flex-1">
                                <h3
                                    class="mb-2 truncate text-base font-semibold text-text-color-100"
                                >
                                    {{ place.name }}
                                </h3>
                                <div class="mb-3 flex items-center gap-2">
                                    <div
                                        class="h-3 w-3 rounded-full border border-background-color-300 shadow-sm"
                                        [style.backgroundColor]="place.color"
                                    ></div>
                                    <span
                                        class="font-mono text-xs text-text-color-300"
                                        >{{ place.color }}</span
                                    >
                                </div>
                                <div
                                    class="flex items-center gap-1 text-xs text-text-color-300"
                                >
                                    <i class="pi pi-map-marker"></i>
                                    <span
                                        >{{ place.latitude.toFixed(4) }},
                                        {{ place.longitude.toFixed(4) }}</span
                                    >
                                </div>
                            </div>

                            <!-- Actions Menu -->
                            <div class="relative">
                                <p-popover #op>
                                    <div
                                        class="flex min-w-[140px] flex-col gap-1 p-2"
                                    >
                                        <button
                                            (click)="
                                                editPlace(place); op.hide()
                                            "
                                            class="flex w-full cursor-pointer items-center gap-3 rounded-lg border-none bg-transparent px-3 py-2 text-left text-sm text-text-color-100 transition-colors duration-200 hover:bg-background-color-200"
                                        >
                                            <i
                                                class="pi pi-pencil text-xs text-main-color-600"
                                            ></i>
                                            <span>Edit</span>
                                        </button>
                                        <button
                                            (click)="
                                                deletePlace(place); op.hide()
                                            "
                                            class="flex w-full cursor-pointer items-center gap-3 rounded-lg border-none bg-transparent px-3 py-2 text-left text-sm text-red-600 transition-colors duration-200 hover:bg-red-50"
                                        >
                                            <i class="pi pi-trash text-xs"></i>
                                            <span>Delete</span>
                                        </button>
                                    </div>
                                </p-popover>
                                <button
                                    #button
                                    class="flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-text-color-300 transition-all duration-200 hover:bg-background-color-200 hover:text-text-color-100 group-hover:opacity-100 lg:opacity-0"
                                    (click)="op.toggle($event)"
                                >
                                    <i class="pi pi-ellipsis-v text-sm"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                }
            }
        </div>
    </div>
</div>
