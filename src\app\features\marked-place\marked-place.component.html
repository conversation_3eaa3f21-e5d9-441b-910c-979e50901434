<div class="relative h-full w-full sm:flex">
    <div class="h-[88dvh] flex-grow">
        <app-map class="size-full" [nodes]="markedPlacesMap()"> </app-map>
        <div class="absolute left-4 top-4 z-[400]">
            <button
                pButton
                icon="pi pi-plus"
                label="Add Place"
                class="!bg-background-color-200 !text-text-color-200 !border-black !border !border-solid"
                routerLink="add"
            ></button>
        </div>
    </div>
    <div
        class="absolute bottom-0 z-[1000] flex h-32 w-full flex-nowrap gap-2 overflow-x-auto overflow-y-hidden p-4 sm:relative sm:h-auto sm:w-fit sm:flex-col sm:flex-wrap sm:overflow-auto"
    >
        @for (place of markedPlaces(); track $index) {
            <p-card class="mb-4 min-w-[300px]">
                <div class="flex items-center justify-between" id="card">
                    <div>
                        <h3 class="text-lg font-semibold">{{ place.name }}</h3>
                        <div class="flex items-center gap-2">
                            <div
                                class="h-4 w-4 rounded-full"
                                [style.backgroundColor]="place.color"
                            ></div>
                            <span>{{ place.color }}</span>
                        </div>
                    </div>
                    <p-popover #op>
                        <div class="flex flex-col gap-4">
                            <a (click)="editPlace(place)" class="flex gap-2">
                                <i class="pi pi-pencil"></i>
                                <div>edit</div></a
                            >
                            <a (click)="deletePlace(place)" class="flex gap-2">
                                <i class="pi pi-trash"></i>
                                <div>delete</div></a
                            >
                        </div>
                    </p-popover>
                    <button
                        #button
                        pButton
                        type="button"
                        icon="pi pi-ellipsis-v"
                        class="p-button-text"
                        (click)="op.toggle($event)"
                    ></button>
                </div>
            </p-card>
        }
    </div>
</div>
