:host {
    display: block;
    height: 100vh;
    overflow: hidden;
    background: #ffffff;
    color: #000000;
}

.p-4 {
    max-width: 800px;
    margin: 0 auto;
    padding: 1rem;
    padding-left: 6rem !important; /* Space for hamburger button */
    height: 100vh;
    box-sizing: border-box;
    overflow-y: auto;
}

@media (max-width: 768px) {
    .p-4 {
        padding: 0.5rem;
        padding-left: 5rem !important; /* Space for hamburger button on mobile */
    }
}

// Card styling
::ng-deep .p-card,
::ng-deep .theme-card {
    background: #ffffff;
    border: none;
    border-radius: 8px;
    box-shadow: none;
    
    .p-card-header {
        background: #ffffff;
        color: #000000;
        font-weight: 600;
        border-bottom: 1px solid #f0f0f0;
        padding: 1rem;
        font-size: 1.3rem;
    }
    
    .p-card-body {
        background: #ffffff;
        padding: 1rem;
    }
}

// Form field styling
.field {
    margin-bottom: 1rem;
    
    label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: #000000;
        font-size: 1rem;
        letter-spacing: 0.01em;
    }
    
    small {
        color: #666666;
        font-size: 0.875rem;
    }
}

// Input styling
::ng-deep .p-inputtext {
    background: #ffffff;
    border: 1px solid #d0d0d0;
    color: #000000;
    border-radius: 4px;
    transition: border-color 0.3s ease;
    padding: 0.75rem;
    font-size: 1rem;
    
    &::placeholder {
        color: #999999;
    }
    
    &:focus {
        border-color: #000000;
        outline: none;
    }
}

// Autocomplete styling
::ng-deep .p-autocomplete {
    .p-inputtext {
        background: #ffffff;
        border: 1px solid #d0d0d0;
        color: #000000;
    }
    
    .p-autocomplete-panel {
        background: #ffffff;
        border: 1px solid #000000;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        
        .p-autocomplete-items {
            .p-autocomplete-item {
                background: #ffffff;
                color: #000000;
                border: none;
                padding: 0.75rem;
                
                &:hover {
                    background: #f0f0f0;
                }
                
                &.p-highlight {
                    background: #000000;
                    color: #ffffff;
                }
            }
        }
    }
}

// Input group styling
::ng-deep .p-inputgroup {
    border-radius: 4px;
    overflow: hidden;
    
    .p-inputgroup-addon {
        background: #ffffff;
        border: 1px solid #d0d0d0;
        color: #000000;
        
        i {
            color: #666666;
        }
    }
    
    .p-inputgroup-addon:first-child {
        border-right: none;
    }
    
    .p-autocomplete .p-inputtext {
        border-left: none;
        border-radius: 0;
    }
}

// Button styling
::ng-deep .p-button {
    background: #000000;
    color: #ffffff;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    letter-spacing: 0.01em;
    width: 100%;
    
    &:hover {
        background: #333333;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    &:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
    }
    
    &:disabled {
        background: #cccccc;
        color: #666666;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }
    
    &.p-button-outlined {
        background: #ffffff;
        color: #000000;
        border: 1px solid #000000;
        
        &:hover {
            background: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
    }
    
    &.flex-shrink-0 {
        min-width: 2.5rem;
        min-height: 2.5rem;
        padding: 0.5rem;
        width: auto;
        background: #000000 !important;
        color: #ffffff !important;
        border: 1px solid #000000 !important;
        
        &:hover {
            background: #333333 !important;
            color: #ffffff !important;
        }
        
        i {
            font-size: 1rem;
            color: #ffffff !important;
        }
    }
}

// Search area styling
.mb-2.flex.gap-2.px-2 {
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background: #fafafa;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    align-items: center;
}

// Map container styling
.h-\[200px\] {
    border: 2px solid #000000;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 0.5rem;
}

// Buttons container
.mt-4.flex.justify-end.gap-2 {
    border-top: 1px solid #f0f0f0;
    padding-top: 1rem;
    margin-top: 1rem;
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
}

@media (min-width: 768px) {
    .mt-4.flex.justify-end.gap-2 {
        flex-direction: row;
        align-items: center;
        gap: 0.75rem;
        
        ::ng-deep .p-button {
            width: auto;
            flex: 1;
        }
    }
}

// Tooltip styling
::ng-deep .p-tooltip {
    background: #000000;
    color: #ffffff;
    border: 1px solid #000000;
    border-radius: 4px;
} 