/* You can add global styles to this file, and also import other style files */

@use './tailwind.css';
@use '../public/layout/layout.scss';
@use 'primeicons/primeicons.css';
@use 'leaflet/dist/leaflet.css';
@use 'leaflet-draw/dist/leaflet.draw.css' as draw;
@use './scrollbar.scss';
@use './overrides.scss';


/* Hide Leaflet layers control */
.leaflet-control-layers,
.leaflet-control-layers-toggle,
.leaflet-control-layers-list {
    display: none !important;
}

:root {

  /* main colors*/
  --main-color-100: #FFFFFF;
  --main-color-200: #EAECF0;
  --main-color-300: #D0D5DD;
  --main-color-400: #CBD0D8;
  --main-color-500: #98A2B3;
  --main-color-600: #D9D9D9;
  --main-color-700: #B7B7B7;
  --main-color-800: #344054;
  --main-color-900: #D9E9FF;
  --main-color-950: #F1F1F1;
  --main-color-1000: #FDFDFD;

  /* primary brand color */
  --primary-brand-color-100: #59ACF9;
  --primary-brand-color-200: #0070FF;
  --primary-brand-color-300: #130856;
  --primary-brand-color-400: rgba(0, 112, 255, 0.08);

  /* secondary brand color */
  --secondary-brand-color-100: #FF00DD;
  --secondary-brand-color-200: #9F10E9;
  --secondary-brand-color-300: rgba(159, 16, 233, 0.08);
  --secondary-brand-color-400: rgba(255, 0, 221, 0.08);
  --secondary-brand-color-500: #AE00F1;

  /* linear gradient */
  --linear-gradient: 90deg, #0070FF 0%, #902EE0 100%;

  /* green colors */
  --green-color-100: #66C888;
  --green-color-200: #17B26A;
  --green-color-300: #66c88814;

  /* orange colors */
  --orange-color-100: #F5DF12;
  --orange-color-200: #E37055;
  --orange-color-300: #F59312;
  --orange-color-400: #FEF5EA;
  --orange-color-500: rgba(245, 147, 18, 0.08);
  --orange-color-600: rgba(242, 215, 0, 0.08);
  --orange-color-700: #D96832;

  /* red colors */
  --red-color-100: #FDA29B;
  --red-color-200: #D92D20;
  --red-color-300: #ffeeec;
  --red-color-400: #d92d2014;
  --red-color-500: #FFEAE9;

  /* pink colors */
  --pink-color-100:#F8ECFE;

  /* shadows */
  --main-shadow-100: 1px 0px 9px 0px rgba(0, 0, 0, 0.09);
  --main-shadow-200: 0px -1px 0px 0px #CCC inset, 1px 0px 0px 0px #EBEBEB inset, -1px 0px 0px 0px #EBEBEB inset, 0px 0px 0px 0px rgba(0, 0, 0, 0.40);
  --main-shadow-600: 0px 12px 16px -4px rgba(36, 36, 36, 0.08), 0px 4px 6px -2px rgba(36, 36, 36, 0.03);
  --main-shadow-700: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);

  --main-shadow-300: 1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset, -1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset, 0px -1px 0px 0px rgba(0, 0, 0, 0.17) inset, 0px 1px 0px 0px rgba(204, 204, 204, 0.50) inset, 0px 4px 6px -2px rgba(26, 26, 26, 0.20);
  --main-shadow-400: 0px 12px 16px -4px rgba(255, 255, 255, 0.08), 0px 4px 6px -2px rgba(255, 255, 255, 0.03);
  --main-shadow-500: 0px 4px 4px 0px rgba(0, 0, 0, 0.18);

  --border-color-400 : #e0e0e0;
}


:root[data-theme="light"] {
  /* background colors */

  --p-dialog-background: #FFFFFF;
  --background-color-100: #FFFFFF;
  --background-color-200: #000000;
  --background-color-300: #BFBFBF;
  --background-color-400: #FBFBFB;
  --background-color-500: #FFFFFF;
  --background-color-600: #CBD0D8;
  --background-color-650: #D4E2FD;
  --background-color-700: #F6F9FE;
  --background-color-800: #F6F9FE;
  --background-color-900: #E8F1FD;
  --background-color-1000: #F3F4F6;
  --background-color-1100: #EAECF0;

  /* text colors */
  --text-color-100: #000000;
  --text-color-200: #FFFFFF;
  --text-color-300: #575757;
  --text-color-400: #303030;
  --text-color-500: #130856;

  /* border colors */
  --border-color-100: #000000;
  --border-color-200: #D0D5DD;
  --border-color-300: #D0D5DD;
  --border-color-400: #F0F0F0;

  /* shadow */
  --shadow-100: 0px 0px 13.333px 2.667px rgba(0, 0, 0, 0.08);
  --shadow-200: 0px 12px 16px -4px rgba(36, 36, 36, 0.08), 0px 4px 6px -2px rgba(36, 36, 36, 0.03);
  --shadow-300: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
}

:root[data-theme="dark"] {
  /* background colors */
  --background-color-100: #000000;
  --background-color-200: #FFFFFF;
  --background-color-300: #2C3541;
  --background-color-400: #1B1B1B;
  --background-color-500: #3F4A5C;
  --background-color-600: #344054;
  --background-color-650: #3F4A5C;
  --background-color-700: #3F4A5C;
  --background-color-800: #4B5464;
  --background-color-900: #E8F1FD;
  --background-color-1000: #3F4A5C;
  --background-color-1100: #98A2B3;

  /* text colors */
  --text-color-100: #FFFFFF;
  --text-color-200: #000000;
  --text-color-300: #969696;
  --text-color-400: #FFFFFF;
  --text-color-500: #130856;

  /* border colors */
  --border-color-100: #FFFFFF;
  --border-color-200: #98A2B3;
  --border-color-300: #3F4A5C;
  --border-color-400: #444B59;

  /* shadow */
  --shadow-100: 0px 0px 13.333px 2.667px rgba(255, 255, 255, 0.08);
  --shadow-200: 0px 12px 16px -4px rgba(255, 255, 255, 0.08), 0px 4px 6px -2px rgba(255, 255, 255, 0.03);
  --shadow-300: 0px 12px 16px -4px rgba(255, 255, 255, 0.08), 0px 4px 6px -2px rgba(255, 255, 255, 0.03);

}
