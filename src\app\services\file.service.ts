import { Injectable, inject } from '@angular/core';
import { HttpService, requestOptions } from './http.service';
import { Observable } from 'rxjs';
import { DestroyRef } from '@angular/core';

@Injectable({
    providedIn: 'root',
})
export class FileService {
    private http = inject(HttpService);
    private destroyRef = inject(DestroyRef);

    /**
     * Get image file by filename
     * @param filename Name of the image file
     * @returns Observable with image blob data
     */
    getImage(
        filename: string,
    ): Observable<{ data: Blob; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: `api/files/images/${filename}`,
            des: this.destroyRef,
            extra: {
                responseType: 'blob' as 'json',
            },
        };
        return this.http.get<Blob>(options);
    }

    /**
     * Get image URL for display
     * @param filename Name of the image file
     * @returns Full URL to the image
     */
    getImageUrl(filename: string): string {
        // Assuming your API base URL is available in environment or http service
        return `api/files/images/${filename}`;
    }
}
