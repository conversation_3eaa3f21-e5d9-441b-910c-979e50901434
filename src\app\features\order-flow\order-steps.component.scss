
// Uber-style color palette
$uber-black: #000000;
$uber-dark: #1a1a1a;
$uber-gray: #5a5a5a;
$uber-light-gray: #9e9e9e;
$uber-bg-gray: #f6f6f6;
$uber-green: #05944f;
$uber-blue: #1676d2;
$uber-white: #ffffff;

.uber-flow-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: $uber-white;
    top: 0;
    left: 0;
    margin: 0;
    padding: 0;
    padding-left: 4rem !important; /* Space for hamburger menu button */
    overflow: hidden;
    pointer-events: auto; /* Re-enable pointer events for content */
    
    @media (max-width: 768px) {
        padding-bottom: env(safe-area-inset-bottom, 0);
        padding-left: 0px !important; /* Smaller space for hamburger button on mobile */
        background: $uber-white;
    }
    
    // --------------------------------
    // 6. HEADER
    // --------------------------------
    
    .uber-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1.5rem 2rem;
        background: $uber-white;
        border-bottom: 1px solid #e0e0e0;
        z-index: 998; /* Lower than hamburger button z-index (1000) */
        flex-shrink: 0;
        min-height: 70px;
        
        @media (max-width: 768px) {
            padding: 1.25rem 1rem;
            padding-top: max(1.25rem, env(safe-area-inset-top));
            min-height: 65px;
            background: $uber-white;
        }
        
        .uber-back-button, .uber-menu-button {
            width: 36px;
            height: 36px;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
            background: $uber-white;
            color: $uber-black;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.15s ease;
            font-size: 14px;
            
            @media (max-width: 768px) {
                width: 40px;
                height: 40px;
                border-radius: 6px;
            }
            
            &:hover:not(:disabled) {
                background: $uber-black;
                color: $uber-white;
                border-color: $uber-black;
            }
            
            &:disabled {
                opacity: 0.4;
                cursor: not-allowed;
            }
        }
        
        .uber-title {
            font-size: 1.125rem;
            font-weight: 400;
            color: $uber-black;
            text-align: center;
            flex: 1;
            margin: 0 1rem;
            letter-spacing: -0.01em;
            
            @media (max-width: 768px) {
                font-size: 1.0625rem;
                font-weight: 400;
                letter-spacing: -0.015em;
            }
        }
    }
    
    // --------------------------------
    // 7. CONTENT AREA
    // --------------------------------
    
    .uber-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        height: 100%;
        min-height: 0;
        background: $uber-white;
        
        @media (max-width: 768px) {
            background: $uber-white;
        }
    }
    
    // --------------------------------
    // 8. LOADING SCREEN
    // --------------------------------
    
    .uber-loading-screen {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 60vh;
        padding: 2rem;
        text-align: center;
        
        @media (max-width: 768px) {
            min-height: 70vh;
            padding: 3rem 1.5rem;
        }
        
        .uber-loading-text {
            font-size: 1.5rem;
            font-weight: 300;
            color: $uber-black;
            margin: 1rem 0 0.5rem;
            
            @media (max-width: 768px) {
                font-size: 1.375rem;
            }
        }
        
        .uber-loading-subtitle {
            font-size: 1rem;
            color: $uber-gray;
            margin: 0;
            max-width: 280px;
            
            @media (max-width: 768px) {
                font-size: 0.9375rem;
            }
        }
    }
    
    // --------------------------------
    // 9. LOCATION STEP
    // --------------------------------
    
    .uber-location-step {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        
        .uber-location-header {
            display: flex;
            align-items: center;
            padding: 1.5rem 1.5rem 1rem;
            flex-shrink: 0;
            
            @media (max-width: 768px) {
                padding: 1.25rem 1rem 0.75rem;
            }
            
            .uber-location-icon {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 1rem;
                background: $uber-black;
                color: $uber-white;
                
                @media (max-width: 768px) {
                    width: 28px;
                    height: 28px;
                    margin-right: 1.25rem;
                }
                
                &.pickup,
                &.dropoff {
                    background: $uber-black;
                    color: $uber-white;
                }
                
                &::before {
                    content: '';
                    width: 10px;
                    height: 10px;
                    background: $uber-white;
                    border-radius: 50%;
                    
                    @media (max-width: 768px) {
                        width: 12px;
                        height: 12px;
                    }
                }
            }
            
            .uber-location-label {
                font-size: 1.125rem;
                font-weight: 400;
                color: $uber-black;
                margin: 0;
                letter-spacing: -0.02em;
                display: flex;
                align-items: center;
                min-height: 24px;
                
                @media (max-width: 768px) {
                    font-size: 1.25rem;
                    font-weight: 400;
                    letter-spacing: -0.03em;
                    min-height: 28px;
                }
            }
        }
        
        .uber-location-confirmed {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            background: $uber-white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin: 0.5rem 1.5rem 0.25rem;
            flex-shrink: 0;
            
            @media (max-width: 768px) {
                padding: 0.75rem;
                border-radius: 12px;
                margin: 0.5rem 1rem 0.25rem;
                border: 1px solid #e0e0e0;
            }
            
            .uber-location-icon.confirmed {
                background: $uber-black;
                color: $uber-white;
                width: 20px;
                height: 20px;
                margin-right: 1rem;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                
                &::before {
                    content: '';
                    width: 8px;
                    height: 8px;
                    background: $uber-white;
                    border-radius: 50%;
                }
            }
            
            .uber-location-details {
                .uber-location-type {
                    font-size: 0.75rem;
                    font-weight: 500;
                    color: #666;
                    margin: 0 0 0.25rem 0;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }
                
                .uber-location-address {
                    font-size: 1rem;
                    color: $uber-black;
                    margin: 0;
                    font-weight: 400;
                }
            }
        }
        
        .uber-location-picker {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            flex-grow: 1;
            
            // Ensure map container gets proper sizing with limited height
            ::ng-deep app-map {
                display: block !important;
                height: 350px !important; // Fixed height instead of 100%
                max-height: 350px !important; // Prevent it from growing
                width: calc(100% - 3rem) !important; // Account for left/right margins
                margin: 0 1.5rem 1rem 1.5rem; // All-around spacing (top, right, bottom, left)
                border: 2px solid #000000; // Black outline
                border-radius: 12px; // Rounded corners
                overflow: hidden; // Ensure map content respects border radius
                flex-shrink: 0; // Don't shrink the map
                
                @media (max-width: 768px) {
                    height: 100dvh !important;
                    max-height: 64dvh !important;
                    width: calc(100% - 2rem) !important; // Account for mobile margins
                    margin: 0 1rem 1rem 1rem; // Smaller margins on mobile
                    border-radius: 16px; // Larger radius on mobile
                }
                
                .leaflet-container {
                    border-radius: inherit; // Inherit border radius from parent
                }
            }
            
            // Ensure the location picker component gets remaining space
            app-location-picker {
                flex: 1;
                display: flex;
                flex-direction: column;
                min-height: 0;
                
                // Ensure space for elements below map
                .uber-selected-location,
                .uber-confirm-container {
                    flex-shrink: 0; // Don't shrink these elements
                }
            }
        }
        
        .uber-marked-places {
            padding: 0 1.5rem;
            flex-shrink: 0;
            
            @media (max-width: 768px) {
                padding: 0 1rem;
            }
            
            .uber-marked-places-scroll {
                display: flex;
                gap: 0.5rem;
                overflow-x: auto;
                overflow-y: hidden;
                padding: 0.5rem 0;
                scroll-behavior: smooth;
                -webkit-overflow-scrolling: touch;
                
                &::-webkit-scrollbar {
                    display: none;
                }
                
                scrollbar-width: none;
                -ms-overflow-style: none;
            }
        }
        
        .uber-marked-place-card {
            flex: 0 0 auto;
            width: fit-content;
            min-width: 80px;
            background: $uber-white;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: all 0.15s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            
            @media (max-width: 768px) {
                min-width: 85px;
                border-radius: 6px;
                padding: 0.875rem 1.125rem;
            }
            
            &:hover {
                background: $uber-black;
                border-color: $uber-black;
                
                .uber-marked-place-name {
                    color: $uber-white;
                }
            }
            
            &:active {
                transform: scale(0.98);
            }
        }
        
        .uber-marked-place-name {
            font-size: 0.8125rem;
            font-weight: 400;
            color: $uber-black;
            margin: 0;
            white-space: nowrap;
            transition: color 0.15s ease;
            
            @media (max-width: 768px) {
                font-size: 0.875rem;
                font-weight: 400;
            }
        }
    }
    
    // --------------------------------
    // 10. CONFIRMATION SECTION
    // --------------------------------
    
    .uber-confirmation {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        padding: 1.5rem;
        
        @media (max-width: 768px) {
            padding: 1rem;
            padding-bottom: 2rem;
        }
        
        .uber-price-card {
            flex-shrink: 0;
            background: $uber-white;
            border: 2px solid $uber-black;
            border-radius: 16px;
            padding: 0;
            margin-bottom: 1.5rem;
            
            @media (max-width: 768px) {
                border-radius: 20px;
                margin-bottom: 1rem;
            }
            
            .uber-car-option {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 1.5rem;
                
                @media (max-width: 768px) {
                    padding: 1.75rem;
                }
                
                .uber-car-info {
                    display: flex;
                    align-items: center;
                    
                    .uber-car-icon {
                        width: 48px;
                        height: 48px;
                        background: $uber-black;
                        color: $uber-white;
                        border-radius: 12px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 1rem;
                        font-size: 1.25rem;
                    }
                    
                    .uber-car-details {
                        .uber-trip-duration {
                            font-size: 0.875rem;
                            color: $uber-gray;
                            margin: 0 0 0.25rem 0;
                        }
                        
                        .uber-car-distance {
                            font-size: 0.875rem;
                            color: $uber-gray;
                            margin: 0;
                        }
                    }
                }
                
                .uber-car-price {
                    .uber-price {
                        font-size: 1.5rem;
                        font-weight: 600;
                        color: $uber-black;
                    }
                }
            }
        }
        
        .uber-route-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;
            min-height: 200px;
            margin-bottom: 1.5rem;
            
            @media (max-width: 768px) {
                margin-bottom: 1rem;
                min-height: 150px;
            }
            
            .uber-route-connector {
                position: absolute;
                left: calc(1.5rem + 6px - 1px);
                top: calc(1.5rem + 12px + 15px);
                bottom: calc(1.5rem + 12px + 15px);
                width: 2px;
                z-index: 1;
                
                background: repeating-linear-gradient(
                    to bottom,
                    #d0d0d0 0px,
                    #d0d0d0 4px,
                    transparent 4px,
                    transparent 8px
                );
                
                @media (max-width: 768px) {
                    left: calc(1.75rem + 6px - 1px);
                    top: calc(1.75rem + 12px + 12px);
                    bottom: calc(1.75rem + 12px + 12px);
                }
            }
            
            .uber-location-section {
                flex-shrink: 0;
                position: relative;
                z-index: 2;
                
                .uber-location-item {
                    display: flex;
                    align-items: center;
                    padding: 1.5rem;
                    background: transparent;
                    border-radius: 16px;
                    
                    @media (max-width: 768px) {
                        padding: 1.75rem;
                        border-radius: 20px;
                    }
                    
                    .uber-location-icon {
                        width: 12px;
                        height: 12px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 1rem;
                        flex-shrink: 0;
                        background: $uber-black;
                        color: $uber-white;
                        
                        &::before {
                            content: '';
                            width: 6px;
                            height: 6px;
                            background: $uber-white;
                            border-radius: 50%;
                        }
                    }
                    
                    .uber-location-details {
                        flex: 1;
                        
                        .uber-location-label {
                            font-size: 0.875rem;
                            font-weight: 600;
                            color: $uber-black;
                            margin: 0 0 0.25rem 0;
                        }
                        
                        .uber-location-address {
                            font-size: 1.375rem;
                            color: #008C45;
                            margin: 0;
                            font-weight: 700;
                        }
                        
                        .uber-location-address-detailed {
                            .uber-location-address-primary {
                                font-size: 1.125rem;
                                color: $uber-black;
                                margin: 0 0 0.25rem 0;
                                font-weight: 600;
                                line-height: 1.3;
                            }
                            
                            .uber-location-address-secondary {
                                font-size: 0.875rem;
                                color: $uber-gray;
                                margin: 0;
                                font-weight: 400;
                                line-height: 1.3;
                            }
                        }
                    }
                }
            }
        }
        
        // --------------------------------
        // 11. BUTTON SECTION (CLEAN & ORGANIZED)
        // --------------------------------
        
        .uber-confirm-section {
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            
            // Base button styles
            .uber-button {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                min-height: 68px;
                padding: 1.5rem 2rem;
                border-radius: 8px;
                font-size: 1rem;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s ease;
                text-transform: none;
                letter-spacing: 0.5px;
                font-family: inherit;
                outline: none;
                box-sizing: border-box;
                
                @media (max-width: 768px) {
                    min-height: 72px;
                    padding: 1.75rem 2rem;
                    font-size: 1.1rem;
                    border-radius: 12px;
                }
                
                &:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                    transform: none;
                }
                
                // Primary button (Confirm) - Black background
                &.uber-button-primary {
                    background: $uber-black;
                    color: $uber-white;
                    border: none;
                    
                    &:hover:not(:disabled) {
                        background: $uber-dark;
                        transform: translateY(-1px);
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                    }
                    
                    &:active:not(:disabled) {
                        transform: translateY(0);
                        background: $uber-black;
                    }
                    
                    &:disabled {
                        background: #666666;
                        color: $uber-white;
                    }
                }
                
                // Secondary button (Cancel) - Outlined & Smaller
                &.uber-button-secondary {
                    background: $uber-white;
                    color: $uber-black;
                    border: 2px solid $uber-black;
                    min-height: 48px !important; // Smaller than primary button
                    padding: 1rem 2rem !important; // Less padding
                    
                    @media (max-width: 768px) {
                        min-height: 52px !important; // Smaller on mobile too
                        padding: 1.25rem 2rem !important; // Less padding on mobile
                    }
                    
                    &:hover:not(:disabled) {
                        background: #f8f8f8;
                        transform: translateY(-1px);
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    }
                    
                    &:active:not(:disabled) {
                        transform: translateY(0);
                        background: $uber-white;
                    }
                    
                    &:disabled {
                        background: #f5f5f5;
                        color: #999999;
                        border-color: #cccccc;
                    }
                }
            }
        }
    }
    
    // --------------------------------
    // 12. DRIVER SEARCH SCREEN
    // --------------------------------
    
    .uber-driver-search {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 80vh;
        padding: 2rem;
        text-align: center;
        
        @media (max-width: 768px) {
            min-height: 85vh;
            padding: 2rem 1.5rem;
        }
        
        .uber-driver-search-content {
            max-width: 400px;
            width: 100%;
            
            // Animated car with search waves
            .uber-car-animation {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 2.5rem;
                
                .uber-car-icon-large {
                    width: 100px;
                    height: 100px;
                    background: $uber-black;
                    color: $uber-white;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 2.5rem;
                    position: relative;
                    z-index: 10;
                    animation: carBounce 2s ease-in-out infinite;
                    
                    @media (max-width: 768px) {
                        width: 90px;
                        height: 90px;
                        font-size: 2.25rem;
                    }
                }
                
                .uber-search-waves {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    
                    .uber-wave {
                        position: absolute;
                        border: 2px solid $uber-black;
                        border-radius: 50%;
                        opacity: 0;
                        animation: waveAnimation 2s ease-out infinite;
                        
                        &.wave-1 {
                            width: 120px;
                            height: 120px;
                            margin-left: -60px;
                            margin-top: -60px;
                            animation-delay: 0s;
                        }
                        
                        &.wave-2 {
                            width: 160px;
                            height: 160px;
                            margin-left: -80px;
                            margin-top: -80px;
                            animation-delay: 0.7s;
                        }
                        
                        &.wave-3 {
                            width: 200px;
                            height: 200px;
                            margin-left: -100px;
                            margin-top: -100px;
                            animation-delay: 1.4s;
                        }
                    }
                }
            }
            
            // Main message
            .uber-search-message {
                margin-bottom: 2rem;
                
                .uber-search-title {
                    font-size: 1.75rem;
                    font-weight: 300;
                    color: $uber-black;
                    margin: 0 0 0.75rem 0;
                    letter-spacing: -0.025em;
                    
                    @media (max-width: 768px) {
                        font-size: 1.5rem;
                    }
                }
                
                .uber-search-subtitle {
                    font-size: 1rem;
                    color: $uber-gray;
                    margin: 0;
                    font-weight: 400;
                    
                    @media (max-width: 768px) {
                        font-size: 0.9375rem;
                    }
                }
            }
            
            // Trip summary card
            .uber-trip-summary {
                background: $uber-white;
                border: 2px solid $uber-black;
                border-radius: 16px;
                padding: 1.5rem;
                margin-bottom: 2rem;
                text-align: left;
                
                @media (max-width: 768px) {
                    border-radius: 20px;
                    padding: 1.75rem;
                }
                
                .uber-trip-route {
                    margin-bottom: 1.5rem;
                    
                    .uber-route-item {
                        display: flex;
                        align-items: center;
                        gap: 1rem;
                        
                        .uber-route-dot {
                            width: 12px;
                            height: 12px;
                            border-radius: 50%;
                            flex-shrink: 0;
                            
                            &.pickup {
                                background: $uber-green;
                            }
                            
                            &.dropoff {
                                background: $uber-black;
                            }
                        }
                        
                        .uber-route-info {
                            display: flex;
                            flex-direction: column;
                            gap: 0.25rem;
                            
                            .uber-route-label {
                                font-size: 0.75rem;
                                color: $uber-gray;
                                font-weight: 500;
                                text-transform: uppercase;
                                letter-spacing: 0.5px;
                            }
                            
                            .uber-route-address {
                                font-size: 0.9375rem;
                                color: $uber-black;
                                font-weight: 500;
                            }
                        }
                    }
                    
                    .uber-route-line {
                        width: 2px;
                        height: 20px;
                        background: #e0e0e0;
                        margin: 0.5rem 0 0.5rem 5px;
                    }
                }
                
                .uber-trip-price {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding-top: 1rem;
                    border-top: 1px solid #e0e0e0;
                    
                    .uber-price-label {
                        font-size: 0.9375rem;
                        color: $uber-gray;
                        font-weight: 500;
                    }
                    
                    .uber-price-value {
                        font-size: 1.25rem;
                        color: $uber-black;
                        font-weight: 700;
                    }
                }
            }
            
            // Cancel button
            .uber-cancel-section {
                .uber-button-cancel {
                    width: 100%;
                    background: $uber-white;
                    color: $uber-black;
                    border: 2px solid $uber-black;
                    border-radius: 8px;
                    padding: 1rem 2rem;
                    font-size: 1rem;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    
                    @media (max-width: 768px) {
                        border-radius: 12px;
                        padding: 1.25rem 2rem;
                        font-size: 1.0625rem;
                    }
                    
                    &:hover {
                        background: #f8f8f8;
                        transform: translateY(-1px);
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    }
                    
                    &:active {
                        transform: translateY(0);
                        background: $uber-white;
                    }
                }
            }
        }
    }
    
    // --------------------------------
    // 13. ANIMATIONS & SPINNERS
    // --------------------------------
    
    .uber-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid $uber-black;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
        
        &.small {
            width: 20px;
            height: 20px;
            border-width: 2px;
            margin-bottom: 0;
        }
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    @keyframes successPulse {
        0% { transform: scale(0.8); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes carBounce {
        0%, 100% {
            transform: translateY(0);
        }
        50% {
            transform: translateY(-8px);
        }
    }
    
    @keyframes waveAnimation {
        0% {
            transform: scale(0.8);
            opacity: 0.8;
        }
        50% {
            transform: scale(1.2);
            opacity: 0.4;
        }
        100% {
            transform: scale(1.6);
            opacity: 0;
        }
    }
}



 