import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { MapComponent } from '../map.component';
import { divIcon, latLng, Layer, Marker, polyline } from 'leaflet';
import { getRandomColor } from '../../helpers/randomColor';

@Component({
    selector: 'app-trip-map-dialog',
    standalone: true,
    imports: [CommonModule, MapComponent],
    templateUrl: './trip-map-dialog.component.html',
})
export class TripMapDialogComponent implements OnInit {
    private config = inject(DynamicDialogConfig);
    private dialogRef = inject(DynamicDialogRef);

    trip!: {
        dropoffPoint: { longitude: number; latitude: number };
        pickupPoint: { longitude: number; latitude: number };
    };
    mapMarkers: Layer[] = [];

    ngOnInit(): void {
        this.trip = this.config.data.trip;

        this.mapMarkers.push(
            marker(
                this.trip.pickupPoint.latitude,
                this.trip.pickupPoint.longitude,
                'green',
            ),
            marker(
                this.trip.dropoffPoint.latitude,
                this.trip.dropoffPoint.longitude,
                'red',
            ),
            // polyline(line, { color: 'blue' }),
        );
    }

    close(): void {
        this.dialogRef.close();
    }

    drawLine() {
        // this.services.ValhallaService.getDirections({
        //     ...this.directionsRequest,
        //     locations: this.nodesLats().map((v) => ({
        //         ...v,
        //         lon: v.lng,
        //         type: 'break',
        //     })),
        // }).subscribe((val) => {
        //     const line: LatLngTuple[] = parseDirectionsGeometry(val.trip.legs);
        //     const r = polyline(line, { color: 'blue' }).bindPopup(
        //         this.getRouteToolTip(val.trip.summary),
        //     );
        //     this.line.set([...this.line(), r]);
        //     this.fit.set(!this.fit());
        // });
    }
}

const marker = (latitude: number, longitude: number, color: string) => {
    return new Marker(latLng(latitude, longitude), {
        draggable: false,
        icon: divIcon({
            iconSize: [36, 36],
            className: '',
            html: `<div class="flex size-10 items-center justify-center rounded-full bg-opacity-30" style="background-color:${color}4d">
                                                  <div class="flex size-8 items-center justify-center rounded-full text-white" style="background-color:${color}">
                                                  </div>
                                              </div>`,
        }),
    });
};
