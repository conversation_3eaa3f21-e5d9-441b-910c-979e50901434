import { Injectable } from '@nestjs/common';
import { Order, Point } from '@prisma/client';
import { PrismaService } from '../common/prisma/prisma.service';
import { ValhallaService } from './valhalla.service';

export interface PricingConfig {
  // Regular time pricing (06:00 - 23:00)
  regularTime: {
    minimumFare: number;
    flagDownFee: number;
    ratePerMinute: number;
    ratePerKm: number;
  };
  // Night time pricing (02:00 - 06:00)
  nightTime: {
    minimumFare: number;
    flagDownFee: number;
    ratePerMinute: number;
    ratePerKm: number;
  };
  // Late night pricing (23:00 - 02:00)
  lateNightTime: {
    minimumFare: number;
    flagDownFee: number;
    ratePerMinute: number;
    ratePerKm: number;
  };
}

export interface OrderWithPoints extends Order {
  pickupPoint?: Point | null;
  dropoffPoint?: Point | null;
}

@Injectable()
export class PricingService {
  private readonly pricingConfig: PricingConfig;

  constructor(
    private prisma: PrismaService,
    private valhallaService: ValhallaService,
  ) {
    // Configurable pricing in USD - balanced rates for market competitiveness
    // These values are configurable and can be adjusted based on market conditions
    this.pricingConfig = {
      regularTime: {
        minimumFare: 1.50,      // $1.50 minimum fare
        flagDownFee: 0.35,      // $0.35 flag down fee
        ratePerMinute: 0.05,    // $0.05 per minute
        ratePerKm: 0.50,        // $0.50 per kilometer
      },
      nightTime: {
        minimumFare: 1.80,      // $1.80 minimum fare (20% higher)
        flagDownFee: 0.45,      // $0.45 flag down fee
        ratePerMinute: 0.06,    // $0.06 per minute
        ratePerKm: 0.60,        // $0.60 per kilometer
      },
      lateNightTime: {
        minimumFare: 1.65,      // $1.65 minimum fare
        flagDownFee: 0.40,      // $0.40 flag down fee
        ratePerMinute: 0.055,   // $0.055 per minute
        ratePerKm: 0.55,        // $0.55 per kilometer
      },
    };
  }

  /**
   * Calculate initial price for an order (shown to client when creating order)
   * Uses Valhalla for accurate route calculation with fallback to Haversine
   * @param order Order with pickup and dropoff points
   * @returns Initial price in USD
   */
  async calculateInitialPrice(
    order: OrderWithPoints,
  ): Promise<number> {
    if (!order.pickupPoint || !order.dropoffPoint) {
      throw new Error('Order must have pickup and dropoff points for pricing calculation');
    }

    // Get Syrian timezone time
    const syrianTime = this.getSyrianTime();
    const pricing = this.getPricingForTime(syrianTime);

    // Use Valhalla to get accurate route information
    const routeInfo = await this.valhallaService.calculateRoute(
      order.pickupPoint.latitude,
      order.pickupPoint.longitude,
      order.dropoffPoint.latitude,
      order.dropoffPoint.longitude,
      'auto' // Use auto costing for car routes
    );

    // Calculate base price: flag down fee + (time * rate) + (distance * rate)
    const basePriceFromTime = routeInfo.time * pricing.ratePerMinute;
    const basePriceFromDistance = routeInfo.distance * pricing.ratePerKm;
    const calculatedPrice = pricing.flagDownFee + basePriceFromTime + basePriceFromDistance;

    // Apply minimum fare
    const finalPrice = Math.max(calculatedPrice, pricing.minimumFare);

    // Round to 2 decimal places
    return Math.round(finalPrice * 100) / 100;
  }

  /**
   * Get Syrian timezone time
   */
  private getSyrianTime(): Date {
    const now = new Date();
    // Syria is UTC+3 (or UTC+2 during standard time, but let's use UTC+3 for simplicity)
    const syrianOffset = 3 * 60; // 3 hours in minutes
    const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
    const syrianTime = new Date(utc + (syrianOffset * 60000));
    return syrianTime;
  }

  /**
   * Get pricing configuration based on Syrian time
   */
  private getPricingForTime(syrianTime: Date): PricingConfig[keyof PricingConfig] {
    const hour = syrianTime.getHours();

    // Night time: 02:00 - 06:00
    if (hour >= 2 && hour < 6) {
      return this.pricingConfig.nightTime;
    }
    // Late night: 23:00 - 02:00 (next day)
    else if (hour >= 23 || hour < 2) {
      return this.pricingConfig.lateNightTime;
    }
    // Regular time: 06:00 - 23:00
    else {
      return this.pricingConfig.regularTime;
    }
  }

} 