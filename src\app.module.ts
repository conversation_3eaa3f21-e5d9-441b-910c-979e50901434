import { Module } from '@nestjs/common';
import { UserModule } from './user/user.module';
import { PrismaModule } from './common/prisma/prisma.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { StopPointModule } from './stop-point/stop-point.module';
import { MarkedPlaceModule } from './marked-place/marked-place.module';
import { OrderModule } from './order/order.module';
import { JwtModule } from '@nestjs/jwt';
import { UltraMsgModule } from './common/ultramsg/ultramsg.module';
import { DriverModule } from './driver/driver.module';
import { FilesModule } from './file/file.module';
import { PricingModule } from './pricing/pricing.module';
import { TransactionModule } from './transaction/transaction.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: '1h' },
      }),
      inject: [ConfigService],
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', '..', 'uploads'),
      serveRoot: '/uploads/',
      exclude: ['/api/{*test}'],
      serveStaticOptions: {
        fallthrough: false,
      },
    }),

    PrismaModule,
    UserModule,
    StopPointModule,
    MarkedPlaceModule,
    OrderModule,
    PricingModule,
    TransactionModule,
    UltraMsgModule,
    DriverModule,
    FilesModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
