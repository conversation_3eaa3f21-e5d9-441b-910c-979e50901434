import { Injectable, inject } from '@angular/core';
import { HttpService, requestOptions } from './http.service';
import { Observable } from 'rxjs';
import { DestroyRef } from '@angular/core';
import {
    User,
    UserWithCar,
    DriverStatus,
    SignUpDto,
    SignInDto,
    RefreshTokenDto,
    VerifyPhoneDto,
    GetUsersDto,
    AuthResponse,
} from '../core/types/tripoos.types';

// Re-export types for convenience
export { DriverStatus } from '../core/types/tripoos.types';
export type {
    User,
    UserWithCar,
    SignUpDto,
    SignInDto,
    RefreshTokenDto,
    VerifyPhoneDto,
    GetUsersDto,
    AuthResponse,
} from '../core/types/tripoos.types';

@Injectable({
    providedIn: 'root',
})
export class UserService {
    private http = inject(HttpService);
    private destroyRef = inject(DestroyRef);

    /**
     * Register a new user
     * @param signUpDto User registration data
     * @returns Observable with AuthResponse containing access token, refresh token and user data
     */
    signUp(
        signUpDto: SignUpDto,
    ): Observable<
        { data: AuthResponse; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/users/auth/sign-up',
            opj: signUpDto,
            des: this.destroyRef,
        };
        return this.http.post<AuthResponse>(options);
    }

    /**
     * Sign in a user
     * @param signInDto User login credentials
     * @returns Observable with AuthResponse containing access token, refresh token and user data
     */
    signIn(
        signInDto: SignInDto,
    ): Observable<
        { data: AuthResponse; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/users/auth/sign-in',
            opj: signInDto,
            des: this.destroyRef,
        };
        return this.http.post<AuthResponse>(options);
    }

    /**
     * Verify a user's phone number
     * @param verifyPhoneDto Phone verification data
     * @returns Observable with AuthResponse containing access token, refresh token and user data
     */
    verifyPhone(
        verifyPhoneDto: VerifyPhoneDto,
    ): Observable<
        { data: AuthResponse; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/users/auth/verify-phone',
            opj: verifyPhoneDto,
            des: this.destroyRef,
        };
        return this.http.post<AuthResponse>(options);
    }

    /**
     * Send verification code to phone number
     * @param phoneNumber User's phone number
     * @returns Observable with success response
     */
    sendVerificationCode(
        phoneNumber: string,
    ): Observable<{ data: any; error: null } | { data: null; error: any }> {
        const options: requestOptions = {
            link: 'api/users/auth/send-verification-code',
            opj: { phone_number: phoneNumber },
            des: this.destroyRef,
        };
        return this.http.post<any>(options);
    }

    /**
     * Refresh the authentication token
     * @param refreshTokenDto Refresh token data
     * @returns Observable with AuthResponse containing new access token, refresh token and user data
     */
    refreshToken(
        refreshTokenDto: RefreshTokenDto,
    ): Observable<
        { data: AuthResponse; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/users/auth/refresh',
            opj: refreshTokenDto,
            des: this.destroyRef,
        };
        return this.http.post<AuthResponse>(options);
    }

    /**
     * Get the current user's profile
     * @returns Observable with User data
     */
    getProfile(): Observable<
        { data: User; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: 'api/users/me',
            des: this.destroyRef,
        };
        return this.http.get<User>(options);
    }

    // This function is not implemented in the backend API
    // Removed: updateProfile

    // These functions are not implemented in the backend API
    // Removed: uploadProfilePhoto, changePassword, forgotPassword, resetPassword

    /**
     * Get all users (admin only)
     * @param driverStatus Optional filter by driver status
     * @returns Observable with array of UserWithCar objects
     */
    getAllUsers(
        driverStatus?: DriverStatus,
    ): Observable<
        { data: UserWithCar[]; error: null } | { data: null; error: any }
    > {
        const options: requestOptions = {
            link: driverStatus
                ? `api/users?driverStatus=${driverStatus}`
                : 'api/users',
            des: this.destroyRef,
        };
        return this.http.get<UserWithCar[]>(options);
    }
}
