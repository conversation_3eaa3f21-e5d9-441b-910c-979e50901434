import { <PERSON>du<PERSON> } from '@nestjs/common';
import { OrderController } from './order.controller';
import { OrderService } from './order.service';
import { JwtModule } from '@nestjs/jwt';
import { PrismaModule } from '../common/prisma/prisma.module';
import { PricingModule } from '../pricing/pricing.module';
import { jwtConfig } from '../common/config/jwt.config';

@Module({
  imports: [
    PrismaModule,
    PricingModule,
    JwtModule.registerAsync(jwtConfig),
  ],
  controllers: [OrderController],
  providers: [OrderService],
})
export class OrderModule {}
