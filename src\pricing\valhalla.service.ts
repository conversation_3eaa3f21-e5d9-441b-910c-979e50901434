import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface RouteResponse {
  distance: number; // in kilometers
  time: number; // in minutes
  success: boolean;
  error?: string;
}

@Injectable()
export class ValhallaService {
  private readonly logger = new Logger(ValhallaService.name);
  private readonly valhallaBaseUrl: string;

  constructor(private configService: ConfigService) {
    // Default to local Valhalla instance, can be configured via environment
    this.valhallaBaseUrl = this.configService.get<string>('VALHALLA_BASE_URL') || 'http://localhost:8002';
  }

  /**
   * Calculate route between two points using Valhalla
   * @param fromLat Starting latitude
   * @param fromLng Starting longitude
   * @param toLat Destination latitude
   * @param toLng Destination longitude
   * @param costing Costing model (auto, taxi, bus, bicycle, pedestrian)
   * @returns Route information with distance and time
   */
  async calculateRoute(
    fromLat: number,
    fromLng: number,
    toLat: number,
    toLng: number,
    costing: string = 'auto'
  ): Promise<RouteResponse> {
    try {
      const routeRequest = {
        locations: [
          { lat: fromLat, lon: fromLng },
          { lat: toLat, lon: toLng }
        ],
        costing: costing,
        units: 'kilometers',
        language: 'en-US'
      };

      const response = await fetch(`${this.valhallaBaseUrl}/route`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(routeRequest)
      });

      if (!response.ok) {
        throw new Error(`Valhalla API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.trip || !data.trip.legs || data.trip.legs.length === 0) {
        throw new Error('Invalid route response from Valhalla');
      }

      const leg = data.trip.legs[0];
      const distanceKm = leg.length; // Valhalla returns distance in kilometers when units=kilometers
      const timeMinutes = leg.time / 60; // Valhalla returns time in seconds, convert to minutes

      return {
        distance: distanceKm,
        time: timeMinutes,
        success: true
      };

    } catch (error) {
      this.logger.error(`Failed to calculate route: ${error.message}`, error.stack);
      
      // Fallback to Haversine calculation if Valhalla fails
      const fallbackDistance = this.calculateHaversineDistance(fromLat, fromLng, toLat, toLng);
      const fallbackTime = this.estimateTimeFromDistance(fallbackDistance);

      return {
        distance: fallbackDistance,
        time: fallbackTime,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Fallback Haversine distance calculation
   */
  private calculateHaversineDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
      Math.cos(this.toRadians(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    
    return distance;
  }

  /**
   * Estimate time from distance (fallback calculation)
   * Assumes average city driving speed of 30 km/h
   */
  private estimateTimeFromDistance(distanceKm: number): number {
    const averageSpeedKmh = 30; // Average city driving speed
    return (distanceKm / averageSpeedKmh) * 60; // Convert hours to minutes
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Health check for Valhalla service
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.valhallaBaseUrl}/status`);
      return response.ok;
    } catch (error) {
      this.logger.warn(`Valhalla health check failed: ${error.message}`);
      return false;
    }
  }
} 