export interface Countries {
    name: string;
    flagUrl: string;
    code: string;
    dial_code: string;
  }

  export const Countries: Countries[] = [
    { code: "AF", name: "Afghanistan", flagUrl: "./country-flags/AF.svg", dial_code: "+93" },
    { code: "AL", name: "Albania", flagUrl: "./country-flags/AL.svg", dial_code: "+355" },
    { code: "DZ", name: "Algeria", flagUrl: "./country-flags/DZ.svg", dial_code: "+213" },
    { code: "AD", name: "Andorra", flagUrl: "./country-flags/AD.svg", dial_code: "+376" },
    { code: "AO", name: "Angola", flagUrl: "./country-flags/AO.svg", dial_code: "+244" },
    { code: "AG", name: "Antigua and Barbuda", flagUrl: "./country-flags/AG.svg", dial_code: "+1-268" },
    { code: "AR", name: "Argentina", flagUrl: "./country-flags/AR.svg", dial_code: "+54" },
    { code: "AM", name: "Armenia", flagUrl: "./country-flags/AM.svg", dial_code: "+374" },
    { code: "AW", name: "Aruba", flagUrl: "./country-flags/AW.svg", dial_code: "+297" },
    { code: "AU", name: "Australia", flagUrl: "./country-flags/AU.svg", dial_code: "+61" },
    { code: "AT", name: "Austria", flagUrl: "./country-flags/AT.svg", dial_code: "+43" },
    { code: "AZ", name: "Azerbaijan", flagUrl: "./country-flags/AZ.svg", dial_code: "+994" },
    { code: "BS", name: "Bahamas", flagUrl: "./country-flags/BS.svg", dial_code: "+1-242" },
    { code: "BH", name: "Bahrain", flagUrl: "./country-flags/BH.svg", dial_code: "+973" },
    { code: "BD", name: "Bangladesh", flagUrl: "./country-flags/BD.svg", dial_code: "+880" },
    { code: "BB", name: "Barbados", flagUrl: "./country-flags/BB.svg", dial_code: "+1-246" },
    { code: "BY", name: "Belarus", flagUrl: "./country-flags/BY.svg", dial_code: "+375" },
    { code: "BE", name: "Belgium", flagUrl: "./country-flags/BE.svg", dial_code: "+32" },
    { code: "BZ", name: "Belize", flagUrl: "./country-flags/BZ.svg", dial_code: "+501" },
    { code: "BJ", name: "Benin", flagUrl: "./country-flags/BJ.svg", dial_code: "+229" },
    { code: "BT", name: "Bhutan", flagUrl: "./country-flags/BT.svg", dial_code: "+975" },
    { code: "BO", name: "Bolivia", flagUrl: "./country-flags/BO.svg", dial_code: "+591" },
    { code: "BA", name: "Bosnia and Herzegovina", flagUrl: "./country-flags/BA.svg", dial_code: "+387" },
    { code: "BW", name: "Botswana", flagUrl: "./country-flags/BW.svg", dial_code: "+267" },
    { code: "BR", name: "Brazil", flagUrl: "./country-flags/BR.svg", dial_code: "+55" },
    { code: "BN", name: "Brunei Darussalam", flagUrl: "./country-flags/BN.svg", dial_code: "+673" },
    { code: "BG", name: "Bulgaria", flagUrl: "./country-flags/BG.svg", dial_code: "+359" },
    { code: "BF", name: "Burkina Faso", flagUrl: "./country-flags/BF.svg", dial_code: "+226" },
    { code: "BI", name: "Burundi", flagUrl: "./country-flags/BI.svg", dial_code: "+257" },
    { code: "CV", name: "Cabo Verde", flagUrl: "./country-flags/CV.svg", dial_code: "+238" },
    { code: "KH", name: "Cambodia", flagUrl: "./country-flags/KH.svg", dial_code: "+855" },
    { code: "CM", name: "Cameroon", flagUrl: "./country-flags/CM.svg", dial_code: "+237" },
    { code: "CA", name: "Canada", flagUrl: "./country-flags/CA.svg", dial_code: "+1" },
    { code: "CF", name: "Central African Republic", flagUrl: "./country-flags/CF.svg", dial_code: "+236" },
    { code: "TD", name: "Chad", flagUrl: "./country-flags/TD.svg", dial_code: "+235" },
    { code: "CL", name: "Chile", flagUrl: "./country-flags/CL.svg", dial_code: "+56" },
    { code: "CN", name: "China", flagUrl: "./country-flags/CN.svg", dial_code: "+86" },
    { code: "CO", name: "Colombia", flagUrl: "./country-flags/CO.svg", dial_code: "+57" },
    { code: "KM", name: "Comoros", flagUrl: "./country-flags/KM.svg", dial_code: "+269" },
    { code: "CG", name: "Congo", flagUrl: "./country-flags/CG.svg", dial_code: "+242" },
    { code: "CR", name: "Costa Rica", flagUrl: "./country-flags/CR.svg", dial_code: "+506" },
    { code: "CI", name: "Ivory Coast", flagUrl: "./country-flags/CI.svg", dial_code: "+225" },
    { code: "HR", name: "Croatia", flagUrl: "./country-flags/HR.svg", dial_code: "+385" },
    { code: "CU", name: "Cuba", flagUrl: "./country-flags/CU.svg", dial_code: "+53" },
    { code: "CW", name: "Curaçao", flagUrl: "./country-flags/CW.svg", dial_code: "+599" },
    { code: "CY", name: "Cyprus", flagUrl: "./country-flags/CY.svg", dial_code: "+357" },
    { code: "CZ", name: "Czechia", flagUrl: "./country-flags/CZ.svg", dial_code: "+420" },
    { code: "DK", name: "Denmark", flagUrl: "./country-flags/DK.svg", dial_code: "+45" },
    { code: "DJ", name: "Djibouti", flagUrl: "./country-flags/DJ.svg", dial_code: "+253" },
    { code: "DM", name: "Dominica", flagUrl: "./country-flags/DM.svg", dial_code: "+1-767" },
    { code: "DO", name: "Dominican Republic", flagUrl: "./country-flags/DO.svg", dial_code: "+1-809" },
    { code: "TL", name: "East Timor", flagUrl: "./country-flags/TL.svg", dial_code: "+670" },
    { code: "EC", name: "Ecuador", flagUrl: "./country-flags/EC.svg", dial_code: "+593" },
    { code: "EG", name: "Egypt", flagUrl: "./country-flags/EG.svg", dial_code: "+20" },
    { code: "SV", name: "El Salvador", flagUrl: "./country-flags/SV.svg", dial_code: "+503" },
    { code: "GQ", name: "Equatorial Guinea", flagUrl: "./country-flags/GQ.svg", dial_code: "+240" },
    { code: "ER", name: "Eritrea", flagUrl: "./country-flags/ER.svg", dial_code: "+291" },
    { code: "EE", name: "Estonia", flagUrl: "./country-flags/EE.svg", dial_code: "+372" },
    { code: "ET", name: "Ethiopia", flagUrl: "./country-flags/ET.svg", dial_code: "+251" },
    { code: "FM", name: "Federated States of Micronesia", flagUrl: "./country-flags/FM.svg", dial_code: "+691" },
    { code: "FJ", name: "Fiji", flagUrl: "./country-flags/FJ.svg", dial_code: "+679" },
    { code: "FI", name: "Finland", flagUrl: "./country-flags/FI.svg", dial_code: "+358" },
    { code: "FR", name: "France", flagUrl: "./country-flags/FR.svg", dial_code: "+33" },
    { code: "GA", name: "Gabon", flagUrl: "./country-flags/GA.svg", dial_code: "+241" },
    { code: "GM", name: "Gambia", flagUrl: "./country-flags/GM.svg", dial_code: "+220" },
    { code: "GE", name: "Georgia", flagUrl: "./country-flags/GE.svg", dial_code: "+995" },
    { code: "DE", name: "Germany", flagUrl: "./country-flags/DE.svg", dial_code: "+49" },
    { code: "GH", name: "Ghana", flagUrl: "./country-flags/GH.svg", dial_code: "+233" },
    { code: "GR", name: "Greece", flagUrl: "./country-flags/GR.svg", dial_code: "+30" },
    { code: "GD", name: "Grenada", flagUrl: "./country-flags/GD.svg", dial_code: "+1-473" },
    { code: "GT", name: "Guatemala", flagUrl: "./country-flags/GT.svg", dial_code: "+502" },
    { code: "GN", name: "Guinea", flagUrl: "./country-flags/GN.svg", dial_code: "+224" },
    { code: "GW", name: "Guinea-Bissau", flagUrl: "./country-flags/GW.svg", dial_code: "+245" },
    { code: "GY", name: "Guyana", flagUrl: "./country-flags/GY.svg", dial_code: "+592" },
    { code: "HT", name: "Haiti", flagUrl: "./country-flags/HT.svg", dial_code: "+509" },
    { code: "JO", name: "Hashemite Kingdom of Jordan", flagUrl: "./country-flags/JO.svg", dial_code: "+962" },
    { code: "HN", name: "Honduras", flagUrl: "./country-flags/HN.svg", dial_code: "+504" },
    { code: "HK", name: "Hong Kong", flagUrl: "./country-flags/HK.svg", dial_code: "+852" },
    { code: "HU", name: "Hungary", flagUrl: "./country-flags/HU.svg", dial_code: "+36" },
    { code: "IS", name: "Iceland", flagUrl: "./country-flags/IS.svg", dial_code: "+354" },
    { code: "IN", name: "India", flagUrl: "./country-flags/IN.svg", dial_code: "+91" },
    { code: "ID", name: "Indonesia", flagUrl: "./country-flags/ID.svg", dial_code: "+62" },
    { code: "IR", name: "Iran", flagUrl: "./country-flags/IR.svg", dial_code: "+98" },
    { code: "IQ", name: "Iraq", flagUrl: "./country-flags/IQ.svg", dial_code: "+964" },
    { code: "IE", name: "Ireland", flagUrl: "./country-flags/IE.svg", dial_code: "+353" },
    { code: "IT", name: "Italy", flagUrl: "./country-flags/IT.svg", dial_code: "+39" },
    { code: "CI", name: "Ivory Coast", flagUrl: "./country-flags/CI.svg", dial_code: "+225" },
    { code: "JM", name: "Jamaica", flagUrl: "./country-flags/JM.svg", dial_code: "+1-876" },
    { code: "JP", name: "Japan", flagUrl: "./country-flags/JP.svg", dial_code: "+81" },
    { code: "KZ", name: "Kazakhstan", flagUrl: "./country-flags/KZ.svg", dial_code: "+7" },
    { code: "KE", name: "Kenya", flagUrl: "./country-flags/KE.svg", dial_code: "+254" },
    { code: "KI", name: "Kiribati", flagUrl: "./country-flags/KI.svg", dial_code: "+686" },
    { code: "KW", name: "Kuwait", flagUrl: "./country-flags/KW.svg", dial_code: "+965" },
    { code: "KG", name: "Kyrgyzstan", flagUrl: "./country-flags/KG.svg", dial_code: "+996" },
    { code: "LA", name: "Laos", flagUrl: "./country-flags/LA.svg", dial_code: "+856" },
    { code: "LV", name: "Latvia", flagUrl: "./country-flags/LV.svg", dial_code: "+371" },
    { code: "LB", name: "Lebanon", flagUrl: "./country-flags/LB.svg", dial_code: "+961" },
    { code: "LS", name: "Lesotho", flagUrl: "./country-flags/LS.svg", dial_code: "+266" },
    { code: "LR", name: "Liberia", flagUrl: "./country-flags/LR.svg", dial_code: "+231" },
    { code: "LY", name: "Libya", flagUrl: "./country-flags/LY.svg", dial_code: "+218" },
    { code: "LI", name: "Liechtenstein", flagUrl: "./country-flags/LI.svg", dial_code: "+423" },
    { code: "LU", name: "Luxembourg", flagUrl: "./country-flags/LU.svg", dial_code: "+352" },
    { code: "MK", name: "Macedonia", flagUrl: "./country-flags/MK.svg", dial_code: "+389" },
    { code: "MG", name: "Madagascar", flagUrl: "./country-flags/MG.svg", dial_code: "+261" },
    { code: "MW", name: "Malawi", flagUrl: "./country-flags/MW.svg", dial_code: "+265" },
    { code: "MY", name: "Malaysia", flagUrl: "./country-flags/MY.svg", dial_code: "+60" },
    { code: "MV", name: "Maldives", flagUrl: "./country-flags/MV.svg", dial_code: "+960" },
    { code: "ML", name: "Mali", flagUrl: "./country-flags/ML.svg", dial_code: "+223" },
    { code: "MT", name: "Malta", flagUrl: "./country-flags/MT.svg", dial_code: "+356" },
    { code: "MH", name: "Marshall Islands", flagUrl: "./country-flags/MH.svg", dial_code: "+692" },
    { code: "MR", name: "Mauritania", flagUrl: "./country-flags/MR.svg", dial_code: "+222" },
    { code: "MU", name: "Mauritius", flagUrl: "./country-flags/MU.svg", dial_code: "+230" },
    { code: "MX", name: "Mexico", flagUrl: "./country-flags/MX.svg", dial_code: "+52" },
    { code: "MC", name: "Monaco", flagUrl: "./country-flags/MC.svg", dial_code: "+377" },
    { code: "MN", name: "Mongolia", flagUrl: "./country-flags/MN.svg", dial_code: "+976" },
    { code: "ME", name: "Montenegro", flagUrl: "./country-flags/ME.svg", dial_code: "+382" },
    { code: "MA", name: "Morocco", flagUrl: "./country-flags/MA.svg", dial_code: "+212" },
    { code: "MZ", name: "Mozambique", flagUrl: "./country-flags/MZ.svg", dial_code: "+258" },
    { code: "MM", name: "Myanmar [Burma]", flagUrl: "./country-flags/MM.svg", dial_code: "+95" },
    { code: "NA", name: "Namibia", flagUrl: "./country-flags/NA.svg", dial_code: "+264" },
    { code: "NR", name: "Nauru", flagUrl: "./country-flags/NR.svg", dial_code: "+674" },
    { code: "NP", name: "Nepal", flagUrl: "./country-flags/NP.svg", dial_code: "+977" },
    { code: "NL", name: "Netherlands", flagUrl: "./country-flags/NL.svg", dial_code: "+31" },
    { code: "NZ", name: "New Zealand", flagUrl: "./country-flags/NZ.svg", dial_code: "+64" },
    { code: "NI", name: "Nicaragua", flagUrl: "./country-flags/NI.svg", dial_code: "+505" },
    { code: "NE", name: "Niger", flagUrl: "./country-flags/NE.svg", dial_code: "+227" },
    { code: "NG", name: "Nigeria", flagUrl: "./country-flags/NG.svg", dial_code: "+234" },
    { code: "KP", name: "North Korea", flagUrl: "./country-flags/KP.svg", dial_code: "+850" },
    { code: "NO", name: "Norway", flagUrl: "./country-flags/NO.svg", dial_code: "+47" },
    { code: "OM", name: "Oman", flagUrl: "./country-flags/OM.svg", dial_code: "+968" },
    { code: "PK", name: "Pakistan", flagUrl: "./country-flags/PK.svg", dial_code: "+92" },
    { code: "PW", name: "Palau", flagUrl: "./country-flags/PW.svg", dial_code: "+680" },
    { code: "PS", name: "Palestine", flagUrl: "./country-flags/PS.svg", dial_code: "+970" },
    { code: "PA", name: "Panama", flagUrl: "./country-flags/PA.svg", dial_code: "+507" },
    { code: "PG", name: "Papua New Guinea", flagUrl: "./country-flags/PG.svg", dial_code: "+675" },
    { code: "PY", name: "Paraguay", flagUrl: "./country-flags/PY.svg", dial_code: "+595" },
    { code: "PE", name: "Peru", flagUrl: "./country-flags/PE.svg", dial_code: "+51" },
    { code: "PH", name: "Philippines", flagUrl: "./country-flags/PH.svg", dial_code: "+63" },
    { code: "PL", name: "Poland", flagUrl: "./country-flags/PL.svg", dial_code: "+48" },
    { code: "PT", name: "Portugal", flagUrl: "./country-flags/PT.svg", dial_code: "+351" },
    { code: "QA", name: "Qatar", flagUrl: "./country-flags/QA.svg", dial_code: "+974" },
    { code: "KR", name: "Republic of Korea", flagUrl: "./country-flags/KR.svg", dial_code: "+82" },
    { code: "LT", name: "Republic of Lithuania", flagUrl: "./country-flags/LT.svg", dial_code: "+370" },
    { code: "MD", name: "Republic of Moldova", flagUrl: "./country-flags/MD.svg", dial_code: "+373" },
    { code: "CG", name: "Republic of the Congo", flagUrl: "./country-flags/CG.svg", dial_code: "+242" },
    { code: "RO", name: "Romania", flagUrl: "./country-flags/RO.svg", dial_code: "+40" },
    { code: "RU", name: "Russia", flagUrl: "./country-flags/RU.svg", dial_code: "+7" },
    { code: "RW", name: "Rwanda", flagUrl: "./country-flags/RW.svg", dial_code: "+250" },
    { code: "LC", name: "Saint Lucia", flagUrl: "./country-flags/LC.svg", dial_code: "+1" },
    { code: "VC", name: "Saint Vincent and the Grenadines", flagUrl: "./country-flags/VC.svg", dial_code: "+1784" },
    { code: "WS", name: "Samoa", flagUrl: "./country-flags/WS.svg", dial_code: "+685" },
    { code: "SM", name: "San Marino", flagUrl: "./country-flags/SM.svg", dial_code: "+378" },
    { code: "ST", name: "São Tomé and Príncipe", flagUrl: "./country-flags/ST.svg", dial_code: "+239" },
    { code: "SA", name: "Saudi Arabia", flagUrl: "./country-flags/SA.svg", dial_code: "+966" },
    { code: "SN", name: "Senegal", flagUrl: "./country-flags/SN.svg", dial_code: "+221" },
    { code: "RS", name: "Serbia", flagUrl: "./country-flags/RS.svg", dial_code: "+381" },
    { code: "SC", name: "Seychelles", flagUrl: "./country-flags/SC.svg", dial_code: "+248" },
    { code: "SL", name: "Sierra Leone", flagUrl: "./country-flags/SL.svg", dial_code: "+232" },
    { code: "SG", name: "Singapore", flagUrl: "./country-flags/SG.svg", dial_code: "+65" },
    { code: "SX", name: "Sint Maarten", flagUrl: "./country-flags/SX.svg", dial_code: "+1721" },
    { code: "SK", name: "Slovakia", flagUrl: "./country-flags/SK.svg", dial_code: "+421" },
    { code: "SI", name: "Slovenia", flagUrl: "./country-flags/SI.svg", dial_code: "+386" },
    { code: "SB", name: "Solomon Islands", flagUrl: "./country-flags/SB.svg", dial_code: "+677" },
    { code: "SO", name: "Somalia", flagUrl: "./country-flags/SO.svg", dial_code: "+252" },
    { code: "ZA", name: "South Africa", flagUrl: "./country-flags/ZA.svg", dial_code: "+27" },
    { code: "SS", name: "South Sudan", flagUrl: "./country-flags/SS.svg", dial_code: "+211" },
    { code: "ES", name: "Spain", flagUrl: "./country-flags/ES.svg", dial_code: "+34" },
    { code: "LK", name: "Sri Lanka", flagUrl: "./country-flags/LK.svg", dial_code: "+94" },
    { code: "SD", name: "Sudan", flagUrl: "./country-flags/SD.svg", dial_code: "+249" },
    { code: "SS", name: "Suriname", flagUrl: "./country-flags/SS.svg", dial_code: "+597" },
    { code: "SZ", name: "Swaziland", flagUrl: "./country-flags/SZ.svg", dial_code: "+268" },
    { code: "SE", name: "Sweden", flagUrl: "./country-flags/SE.svg", dial_code: "+46" },
    { code: "CH", name: "Switzerland", flagUrl: "./country-flags/CH.svg", dial_code: "+41" },
    { code: "SY", name: "Syria", flagUrl: "./country-flags/SY.svg", dial_code: "+963" },
    { code: "TJ", name: "Tajikistan", flagUrl: "./country-flags/TJ.svg", dial_code: "+992" },
    { code: "TZ", name: "Tanzania", flagUrl: "./country-flags/TZ.svg", dial_code: "+255" },
    { code: "TH", name: "Thailand", flagUrl: "./country-flags/TH.svg", dial_code: "+66" },
    { code: "TG", name: "Togo", flagUrl: "./country-flags/TG.svg", dial_code: "+228" },
    { code: "TO", name: "Tonga", flagUrl: "./country-flags/TO.svg", dial_code: "+676" },
    { code: "TT", name: "Trinidad and Tobago", flagUrl: "./country-flags/TT.svg", dial_code: "+1868" },
    { code: "TN", name: "Tunisia", flagUrl: "./country-flags/TN.svg", dial_code: "+216" },
    { code: "TR", name: "Turkey", flagUrl: "./country-flags/TR.svg", dial_code: "+90" },
    { code: "TM", name: "Turkmenistan", flagUrl: "./country-flags/TM.svg", dial_code: "+993" },
    { code: "TV", name: "Tuvalu", flagUrl: "./country-flags/TV.svg", dial_code: "+688" },
    { code: "VI", name: "U.S. Virgin Islands", flagUrl: "./country-flags/VI.svg", dial_code: "+1" },
    { code: "UG", name: "Uganda", flagUrl: "./country-flags/UG.svg", dial_code: "+256" },
    { code: "UA", name: "Ukraine", flagUrl: "./country-flags/UA.svg", dial_code: "+380" },
    { code: "AE", name: "United Arab Emirates", flagUrl: "./country-flags/AE.svg", dial_code: "+971" },
    { code: "GB", name: "United Kingdom", flagUrl: "./country-flags/GB.svg", dial_code: "+44" },
    { code: "US", name: "United States", flagUrl: "./country-flags/US.svg", dial_code: "+1" },
    { code: "UY", name: "Uruguay", flagUrl: "./country-flags/UY.svg", dial_code: "+598" },
    { code: "UZ", name: "Uzbekistan", flagUrl: "./country-flags/UZ.svg", dial_code: "+998" },
    { code: "VU", name: "Vanuatu", flagUrl: "./country-flags/VU.svg", dial_code: "+678" },
    { code: "VA", name: "Vatican City", flagUrl: "./country-flags/VA.svg", dial_code: "+379" },
    { code: "VE", name: "Venezuela", flagUrl: "./country-flags/VE.svg", dial_code: "+58" },
    { code: "VN", name: "Vietnam", flagUrl: "./country-flags/VN.svg", dial_code: "+84" },
    { code: "YE", name: "Yemen", flagUrl: "./country-flags/YE.svg", dial_code: "+967" },
    { code: "ZM", name: "Zambia", flagUrl: "./country-flags/ZM.svg", dial_code: "+260" },
    { code: "ZW", name: "Zimbabwe", flagUrl: "./country-flags/ZW.svg", dial_code: "+263" }
  ]
