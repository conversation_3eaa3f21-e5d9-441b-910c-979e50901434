import { Component, computed, input, model, signal } from '@angular/core';
import {
    <PERSON><PERSON><PERSON>er,
    FormGroup,
    FormsModule,
    ReactiveFormsModule,
    Validators,
} from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { divIcon, latLng, LatLng, Layer, marker, MapOptions } from 'leaflet';
import { AutoComplete } from 'primeng/autocomplete';
import { ButtonModule } from 'primeng/button';
import { ColorPickerModule } from 'primeng/colorpicker';
import { InputTextModule } from 'primeng/inputtext';
import { Tooltip } from 'primeng/tooltip';
import { debounceTime, distinct, filter, map, switchMap } from 'rxjs';
import { StopPointService } from '../../../services';
import { NominatimService } from '../../../services/nominatim.service';
import { MapComponent } from '../../../shared/components/map.component';
import { injectMany } from '../../../shared/helpers/injectMany';
import { getRandomColor } from '../../../shared/helpers/randomColor';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { GeolocationService } from '../../../services/geolocation.service';

@Component({
    selector: 'app-add-stop-points',
    standalone: true,
    imports: [
        FormsModule,
        ReactiveFormsModule,
        ButtonModule,
        InputTextModule,
        ColorPickerModule,
        MapComponent,
        RouterLink,
        AutoComplete,
        Tooltip,
    ],
    templateUrl: './add-stop-points.component.html',
})
export class AddStopPointsComponent {
    services = injectMany({
        FormBuilder,
        StopPointService,
        Router,
        NominatimService,
        GeolocationService,
    });

    id = input.required<string>();

    marked = computed(() => {
        this.services.StopPointService.getStopPointById(this.id()).subscribe(
            (val) => {
                if (val.data) {
                    this.placeForm.setValue(val.data);
                    this.selectedLocation.set(
                        latLng(val.data.latitude, val.data.longitude),
                    );
                    this.options.set({
                        center: latLng(val.data.latitude, val.data.longitude),
                        zoom: 15,
                    });
                }
                return val.data;
            },
        );
    });

    placeForm: FormGroup = this.services.FormBuilder.group({
        name: ['', Validators.required],
        // color: [getRandomColor(), Validators.required],
    });

    // Search functionality
    search = model<string>('');
    searchObs = toObservable(this.search).pipe(
        filter((val) => !!val),
        distinct(),
        debounceTime(2000),
        switchMap((val: string) => {
            return this.services.NominatimService.forwardGeocode(val).pipe(
                map((val) => {
                    return val.map((item) => {
                        return {
                            display_name: item.display_name,
                            latlng: [item.lat, item.lon],
                        };
                    });
                }),
            );
        }),
    );
    searchRes$ = toSignal(this.searchObs);

    // Map state
    selectedLocation = signal<LatLng | null>(null);
    mapMarker = signal<Layer[]>([]);
    options = signal<MapOptions>({});

    ngOnInit() {}

    handleMapClick(event: { latlng: LatLng }) {
        this.selectedLocation.set(event.latlng);

        const color = getRandomColor();
        const newMarker = marker(event.latlng, {
            draggable: false,
            icon: divIcon({
                iconSize: [36, 36],
                className: '',
                html: `<div class="flex size-10 items-center justify-center rounded-full bg-opacity-30" style="background-color:${color}4d">
                <div class="flex size-8 items-center justify-center rounded-full text-white" style="background-color:${color}">
                </div>
              </div>`,
            }),
        });

        this.mapMarker.set([newMarker]);
    }

    pointGeoLocation() {
        this.services.GeolocationService.getUserCountry().subscribe((data) => {
            this.addMarkerOnClick({
                latlng: latLng(data.latitude, data.longitude),
            });
        });
    }
    addMarkerOnClick(event: { latlng: LatLng }) {
        const clickedLatLng = event.latlng;
        const newMarker = marker(clickedLatLng, {
            draggable: true,
            icon: divIcon({
                iconSize: [36, 36],
                className: '',
                html: `<div class="flex size-10 items-center justify-center rounded-full bg-opacity-30" style="background-color:#0000ff4d">
                          <div class="flex size-8 items-center justify-center rounded-full text-white" style="background-color:#0000ff">
                          </div>
                      </div>`,
            }),
        })
            .on('dragend', () => {
                const r = this.mapMarker();
                this.mapMarker.set([]);
                this.mapMarker.set(r);
            })
            .on('click', () => {});

        this.mapMarker.set([newMarker]);
    }

    afterSearch(event: any) {
        this.selectedLocation.set(latLng(event.latlng));
        this.addMarkerOnClick(event);
        this.options.set({ center: event.latlng, zoom: 12 });
        this.search.set('');
    }

    savePlace() {
        const location = this.selectedLocation();
        if (this.placeForm.valid && location) {
            const newPlace = {
                name: this.placeForm.get('name')?.value,
                color: this.placeForm.get('color')?.value,
                latitude: location.lat,
                longitude: location.lng,
            };

            let req = this.services.StopPointService.createStopPoint(newPlace);
            if (this.id()) {
                req = this.services.StopPointService.updateStopPoint(
                    this.id(),
                    newPlace,
                );
            }
            req.subscribe(() => {
                this.services.Router.navigate(['/main', 'stop-points']);
            });
        }
    }
}
