import {
    Component,
    computed,
    inject,
    input,
    output,
    signal,
} from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import {
    LeafletControlLayersConfig,
    LeafletModule,
} from '@bluehalo/ngx-leaflet';
import { LeafletDrawModule } from '@bluehalo/ngx-leaflet-draw';
import {
    featureGroup,
    FeatureGroup,
    LatLng,
    latLng,
    Layer,
    LayerGroup,
    LeafletMouseEvent,
    Map,
    MapOptions,
    tileLayer,
} from 'leaflet';

import { map } from 'rxjs';
import { GeolocationService } from '../../services/geolocation.service';

export interface changeNodeDto {
    event: any;
    layers: FeatureGroup;
}

@Component({
    selector: 'app-map',
    template: `
        <div
            class="h-[82vh]"
            leaflet
            [leafletOptions]="options$()"
            (leafletClick)="clickMap.emit($event)"
            [leafletZoom]="zoom$()"
            [leafletCenter]="center$()"
            (leafletMapReady)="setMap($event)"
        >
            @if (shaowDraw$()) {
                <div leafletDraw [leafletDrawOptions]="drawOptions"></div>
                <!-- (leafletDrawCreated)="onDrawCreated($event)"
                    (leafletDrawCreated)="onDrawCreated($event)"
                    (leafletDrawEditStop)="onDrawCreated($event)"
                    (leafletDrawDeleteStop)="onDrawCreated($event)" -->
            }
            <div [leafletLayer]="drawnItems"></div>
        </div>
    `,
    standalone: true,
    imports: [LeafletModule, LeafletDrawModule],
})
export class MapComponent {
    OpenStreetLyr = tileLayer(
        'http://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
        {
            maxZoom: 22,
            attribution: '..',
        },
    );

    layersControl = signal<LeafletControlLayersConfig>({
        baseLayers: {
            'Open Street Map': this.OpenStreetLyr,
        },
        overlays: {},
    });
    overlayLayers = input<{ [k: string]: LayerGroup }>();
    showLayersControl = input<boolean>(true);
    overlayLayers$ = toObservable(this.overlayLayers).pipe(
        map((data) => {
            this.layersControl.update((val) => {
                return { ...val, overlays: { ...data } };
            });
        }),
    );
    fit = input<boolean>();
    fit$ = toObservable(this.fit).pipe(
        map(() => {
            if (this.map && this.drawnItems.getBounds()) {
                this.map.fitBounds(this.drawnItems.getBounds());
            }
        }),
    );
    nodes = input<Layer[]>();
    nodes$ = toObservable(this.nodes).pipe(
        map((data) => {
            this.drawnItems.clearLayers();
            if (data && data.length > 0) {
                data.map((point) => {
                    this.drawnItems.addLayer(point);
                });
            }
        }),
    );

    draw = input<{
        polyline?: boolean;
        marker?: boolean;
        circlemarker?: boolean;
        rectangle?: boolean;
        polygon?: boolean;
        circle?: boolean;
    }>();
    draw$ = toObservable(this.draw).pipe(
        map((data) => {
            if (data) {
                this.shaowDraw$.set(true);
                this.drawOptions.draw = {
                    polyline: data.polyline,
                    marker: data.marker,
                    circlemarker: data.circlemarker,
                    rectangle: data.rectangle,
                    polygon: data.polygon,
                    circle: data.circle,
                };
            }
        }),
    );
    shaowDraw$ = signal(false);

    clickMap = output<LeafletMouseEvent>();

    drawnItems: FeatureGroup = featureGroup();

    drawOptions: any = {
        position: 'topright',
        draw: {
            polyline: false,
            marker: false,
            circlemarker: false,
            rectangle: false,
            polygon: false,
            circle: false,
        },
        edit: {
            featureGroup: this.drawnItems,
        },
        remove: false,
    };

    baseOptions = signal<MapOptions>({
        layers: [this.OpenStreetLyr],
        zoom: 12,
        center: latLng(35, 38),
        zoomControl: false,
    });
    options = input<MapOptions>();
    changeOptions = signal<MapOptions>({});
    options$ = computed<MapOptions>(() => {
        const o = {
            ...this.baseOptions(),
            ...this.options(),
            ...this.changeOptions(),
        };
        return o;
    });
    zoom$ = computed(() => {
        return this.options$().zoom as number;
    });
    center$ = computed<LatLng>(() => {
        return this.options$().center as LatLng;
    });

    map!: Map;
    setMap(map: Map) {
        this.map = map;
    }

    geolocationService = inject(GeolocationService);
    ngOnInit(): void {
        this.nodes$.subscribe();
        this.draw$.subscribe();
        this.overlayLayers$.subscribe();
        this.fit$.subscribe();

        this.geolocationService.getUserCountry().subscribe((data) => {
            this.baseOptions.set({
                ...this.baseOptions(),
                center: latLng(data.latitude, data.longitude),
            });
        });
    }
}
