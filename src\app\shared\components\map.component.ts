import {
    Component,
    computed,
    inject,
    input,
    output,
    signal,
} from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import {
    LeafletControlLayersConfig,
    LeafletModule,
} from '@bluehalo/ngx-leaflet';
import { LeafletDrawModule } from '@bluehalo/ngx-leaflet-draw';
import {
    featureGroup,
    FeatureGroup,
    LatLng,
    latLng,
    Layer,
    LayerGroup,
    LeafletMouseEvent,
    Map,
    MapOptions,
    marker,
    tileLayer,
} from 'leaflet';

import { map } from 'rxjs';
import { GeolocationService } from '../../services/geolocation.service';

export interface changeNodeDto {
    event: any;
    layers: FeatureGroup;
}

/**
 * Map Component with Auto-Fit Functionality
 *
 * This component automatically fits the map bounds to include all layers when:
 * - New nodes are added via the [nodes] input
 * - New overlay layers are added via the [overlayLayers] input
 * - The [fit] input is triggered manually
 *
 * Auto-fit behavior can be controlled with the [autoFit] input (default: true)
 *
 * @example
 * <app-map [nodes]="markers" [autoFit]="true"></app-map>
 * <app-map [overlayLayers]="layers" [autoFit]="false"></app-map>
 */
@Component({
    selector: 'app-map',
    template: `
        <div
            class="h-[82vh]"
            leaflet
            [leafletOptions]="options$()"
            (leafletClick)="clickMap.emit($event)"
            [leafletZoom]="zoom$()"
            [leafletCenter]="center$()"
            (leafletMapReady)="setMap($event)"
        >
            @if (shaowDraw$()) {
                <div leafletDraw [leafletDrawOptions]="drawOptions"></div>
                <!-- (leafletDrawCreated)="onDrawCreated($event)"
                    (leafletDrawCreated)="onDrawCreated($event)"
                    (leafletDrawEditStop)="onDrawCreated($event)"
                    (leafletDrawDeleteStop)="onDrawCreated($event)" -->
            }
            <div [leafletLayer]="drawnItems"></div>
        </div>
    `,
    standalone: true,
    imports: [LeafletModule, LeafletDrawModule],
})
export class MapComponent {
    OpenStreetLyr = tileLayer(
        'http://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
        {
            maxZoom: 22,
            attribution: '..',
        },
    );

    layersControl = signal<LeafletControlLayersConfig>({
        baseLayers: {
            'Open Street Map': this.OpenStreetLyr,
        },
        overlays: {},
    });
    overlayLayers = input<{ [k: string]: LayerGroup }>();
    showLayersControl = input<boolean>(true);
    overlayLayers$ = toObservable(this.overlayLayers).pipe(
        map((data) => {
            this.layersControl.update((val) => {
                return { ...val, overlays: { ...data } };
            });

            // Auto-fit bounds when new overlay layers are added
            if (this.autoFit() && data && Object.keys(data).length > 0) {
                setTimeout(() => this.fitBoundsToAllLayers(), 100);
            }
        }),
    );
    fit = input<boolean>();
    autoFit = input<boolean>(true); // New input to control auto-fitting behavior

    fit$ = toObservable(this.fit).pipe(
        map(() => {
            this.fitBoundsToAllLayers();
        }),
    );

    nodes = input<Layer[]>();
    nodes$ = toObservable(this.nodes).pipe(
        map((data) => {
            this.drawnItems.clearLayers();
            if (data && data.length > 0) {
                data.map((point) => {
                    this.drawnItems.addLayer(point);
                });

                // Auto-fit bounds when new nodes are added
                if (this.autoFit()) {
                    setTimeout(() => this.fitBoundsToAllLayers(), 100);
                }
            }
        }),
    );

    draw = input<{
        polyline?: boolean;
        marker?: boolean;
        circlemarker?: boolean;
        rectangle?: boolean;
        polygon?: boolean;
        circle?: boolean;
    }>();
    draw$ = toObservable(this.draw).pipe(
        map((data) => {
            if (data) {
                this.shaowDraw$.set(true);
                this.drawOptions.draw = {
                    polyline: data.polyline,
                    marker: data.marker,
                    circlemarker: data.circlemarker,
                    rectangle: data.rectangle,
                    polygon: data.polygon,
                    circle: data.circle,
                };
            }
        }),
    );
    shaowDraw$ = signal(false);

    clickMap = output<LeafletMouseEvent>();

    drawnItems: FeatureGroup = featureGroup();

    drawOptions: any = {
        position: 'topright',
        draw: {
            polyline: false,
            marker: false,
            circlemarker: false,
            rectangle: false,
            polygon: false,
            circle: false,
        },
        edit: {
            featureGroup: this.drawnItems,
        },
        remove: false,
    };

    baseOptions = signal<MapOptions>({
        layers: [this.OpenStreetLyr],
        zoom: 12,
        center: latLng(35, 38),
        zoomControl: false,
    });
    options = input<MapOptions>();
    changeOptions = signal<MapOptions>({});
    options$ = computed<MapOptions>(() => {
        const o = {
            ...this.baseOptions(),
            ...this.options(),
            ...this.changeOptions(),
        };
        return o;
    });
    zoom$ = computed(() => {
        return this.options$().zoom as number;
    });
    center$ = computed<LatLng>(() => {
        return this.options$().center as LatLng;
    });

    map!: Map;
    setMap(map: Map) {
        this.map = map;
    }

    /**
     * Fit map bounds to include all layers (nodes and overlay layers)
     */
    fitBoundsToAllLayers(): void {
        if (!this.map) return;

        const allBounds: LatLng[] = [];

        // Collect bounds from drawnItems (nodes)
        if (this.drawnItems.getLayers().length > 0) {
            this.drawnItems.eachLayer((layer: any) => {
                if (layer.getLatLng) {
                    // For markers
                    allBounds.push(layer.getLatLng());
                } else if (layer.getBounds) {
                    // For shapes with bounds
                    const bounds = layer.getBounds();
                    allBounds.push(bounds.getNorthEast());
                    allBounds.push(bounds.getSouthWest());
                } else if (layer.getLatLngs) {
                    // For polylines/polygons
                    const latLngs = layer.getLatLngs();
                    if (Array.isArray(latLngs)) {
                        latLngs.forEach((latlng: any) => {
                            if (Array.isArray(latlng)) {
                                latlng.forEach((ll: LatLng) =>
                                    allBounds.push(ll),
                                );
                            } else {
                                allBounds.push(latlng);
                            }
                        });
                    }
                }
            });
        }

        // Collect bounds from overlay layers
        const overlays = this.overlayLayers();
        if (overlays) {
            Object.values(overlays).forEach((layerGroup: LayerGroup) => {
                layerGroup.eachLayer((layer: any) => {
                    if (layer.getLatLng) {
                        allBounds.push(layer.getLatLng());
                    } else if (layer.getBounds) {
                        const bounds = layer.getBounds();
                        allBounds.push(bounds.getNorthEast());
                        allBounds.push(bounds.getSouthWest());
                    } else if (layer.getLatLngs) {
                        const latLngs = layer.getLatLngs();
                        if (Array.isArray(latLngs)) {
                            latLngs.forEach((latlng: any) => {
                                if (Array.isArray(latlng)) {
                                    latlng.forEach((ll: LatLng) =>
                                        allBounds.push(ll),
                                    );
                                } else {
                                    allBounds.push(latlng);
                                }
                            });
                        }
                    }
                });
            });
        }

        // Fit bounds if we have any points
        if (allBounds.length > 0) {
            if (allBounds.length === 1) {
                // If only one point, center on it with a reasonable zoom
                this.map.setView(allBounds[0], 15);
            } else {
                // Create bounds from all points and fit
                const group = featureGroup();
                allBounds.forEach((latlng) => {
                    group.addLayer(marker(latlng));
                });
                this.map.fitBounds(group.getBounds(), {
                    padding: [20, 20], // Add some padding around the bounds
                    maxZoom: 16, // Prevent zooming in too much
                });
            }
        }
    }

    geolocationService = inject(GeolocationService);
    ngOnInit(): void {
        this.nodes$.subscribe();
        this.draw$.subscribe();
        this.overlayLayers$.subscribe();
        this.fit$.subscribe();

        this.geolocationService.getUserCountry().subscribe((data) => {
            this.baseOptions.set({
                ...this.baseOptions(),
                center: latLng(data.latitude, data.longitude),
            });
        });
    }
}
