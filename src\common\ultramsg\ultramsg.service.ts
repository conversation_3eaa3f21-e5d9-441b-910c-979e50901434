import { PrismaService } from './../prisma/prisma.service';
// src/ultramsg/ultramsg.service.ts
import { Injectable, BadRequestException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class UltraMsgService {
  private readonly apiUrl = 'https://api.ultramsg.com';
  private readonly instanceId = process.env.ULTRA_MSG_INSTANCE_ID;
  private readonly token = process.env.ULTRA_MSG_TOKEN;

  private readonly VERIFICATION_CODE_EXPIRATION_TIME = 1000 * 60 * 5; // 5 minutes


  constructor(
    private readonly httpService: HttpService,
    private prisma: PrismaService,
  ) {}

  async sendVerificationCode(phoneNumber: string): Promise<any> {
    const verificationCode = this.generateVerificationCode();
    const msg = 'your verification code is: ' + verificationCode;
    //this.sendMessage(phoneNumber, msg);
    const now = new Date();

    try {
      const existingUser = await this.prisma.user.findUnique({
        where: { phoneNumber },
      });

      if (existingUser) {
        await this.prisma.user.update({
          where: { id: existingUser.id },
          data: {
            verificationCode,
            verificationCodeSentAt: now,
          },
        });
      }
      return verificationCode;
    } catch (error) {
      console.error('Error in sendVerificationCode:', error);
      throw new BadRequestException('Failed to process verification code request');
    }
  }

  async verifyCode(phoneNumber: string, code: string): Promise<boolean> {
    const user = await this.prisma.user.findFirst({
      where: { phoneNumber },
    });

    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (user.isPhoneVerified) {
      throw new BadRequestException('Phone number is already verified');
    }

    if (user.verificationCode !== code) {
      throw new BadRequestException('Invalid verification code');
    }

    if (user.verificationCode !== code) {
      throw new BadRequestException('Invalid verification code');
    }

    if (user.verificationCodeSentAt && user.verificationCodeSentAt < new Date(Date.now() - this.VERIFICATION_CODE_EXPIRATION_TIME)) {
      throw new BadRequestException('Verification code has expired');
    }

    await this.prisma.user.update({
      where: { id: user.id },
      data: {
        isPhoneVerified: true,
        verificationCode: null,
      },
    });

    return true;
  }

  private async sendMessage(phoneNumber: string, msg: string) {
    const url = `${this.apiUrl}/${this.instanceId}/messages/chat`;

    const data = {
      token: this.token,
      to: phoneNumber,
      body: msg,
    };

    try {
      const response = await firstValueFrom(this.httpService.post(url, data));
      return response.data;
    } catch (error) {
      throw new Error(`Failed to send WhatsApp message: ${error.message}`);
    }
  }

  private generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }
}
