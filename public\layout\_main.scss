.layout-main-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    justify-content: space-between;
    padding: 0;
    transition: margin-left var(--layout-section-transition-duration);
}

.layout-main {
    flex: 1 1 auto;
    padding-bottom: 0;
}

.layout-content-padded {
    padding: 2rem;
    padding-left: 6rem; /* Space for hamburger menu button */
}

@media (max-width: 768px) {
    .layout-content-padded {
        padding: 1rem;
        padding-left: 5rem; /* Smaller space for mobile */
    }
}

.layout-menu-button {
    position: fixed;
    top: 1rem;
    left: 0;
    z-index: 1000;
    width: 3.5rem;
    height: 3.5rem;
    background-color: var(--background-color-200);
    color: var(--text-color-200);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all var(--element-transition-duration);
    border-radius: 0 1rem 1rem 0;
    transform: translateX(-0.5rem);

    &:hover {
        transform: translateX(0);
    }

    i {
        position: relative;
        transition: transform var(--element-transition-duration);
    }

    &:hover i {
        transform: translateX(2px);
    }

    &.active {
        transform: translateX(0);

        i {
            transform: rotate(180deg);
        }

        &:hover {
            transform: translateX(-0.2rem);
        }
    }
}
