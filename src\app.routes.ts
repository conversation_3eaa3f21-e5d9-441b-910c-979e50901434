import { Routes } from '@angular/router';
import { AppLayout } from './app/layout/component/app.layout';
import { isAuthenticatedGuard } from './app/shared/guards/auth.guard';

export const appRoutes: Routes = [
    {
        path: '',
        loadChildren: () =>
            import('./app/auth/auth.routes').then((c) => c.authRoutes),
    },
    {
        path: 'main',
        component: AppLayout,
        canActivate: [isAuthenticatedGuard()],
        children: [
            {
                path: '',
                pathMatch: 'full',
                loadComponent: () =>
                    import('./app/features/role-redirect/role-redirect.component').then(
                        (c) => c.RoleRedirectComponent,
                    ),
            },
            {
                path: 'marked-places',
                loadChildren: () =>
                    import(
                        './app/features/marked-place/marked-place.routes'
                    ).then((c) => c.MARKED_PLACE_ROUTES),
            },
            {
                path: 'stop-points',
                loadChildren: () =>
                    import(
                        './app/features/stop-points/stop-points.routes'
                    ).then((c) => c.STOP_POINTS_ROUTES),
            },
            {
                path: 'trips-history',
                loadComponent: () =>
                    import(
                        './app/features/trips-history/trips-history.component'
                    ).then((c) => c.TripsHistoryComponent),
            },
            {
                path: 'find-orders',
                loadComponent: () =>
                    import(
                        './app/features/find-orders/find-orders.component'
                    ).then((c) => c.FindOrdersComponent),
            },
            {
                path: 'driver-profile',
                loadComponent: () =>
                    import(
                        './app/features/driver-profile/driver-profile.component'
                    ).then((c) => c.DriverProfileComponent),
            },
            {
                path: 'drivers-evaluation',
                loadComponent: () =>
                    import(
                        './app/features/drivers-evaluation/drivers-evaluation.component'
                    ).then((c) => c.DriversEvaluationComponent),
            },
            {
                path: 'my-trips/:id',
                loadComponent: () =>
                    import('./app/features/my-trips/my-trips.component').then(
                        (c) => c.MyTripsComponent,
                    ),
            },
            {
                path: 'trip/:id',
                loadComponent: () =>
                    import('./app/features/trip-info/trip-info.component').then(
                        (c) => c.TripInfoComponent,
                    ),
            },
            {
                path: 'transactions',
                loadComponent: () =>
                    import('./app/features/transactions/transactions.component').then(
                        (c) => c.TransactionsComponent,
                    ),
            },
            {
                path: 'order',
                loadComponent: () =>
                    import('./app/features/order-flow/order-steps.component').then(
                        (c) => c.OrderStepsComponent,
                    ),
            },
            {
                path: 'user-profile',
                loadComponent: () =>
                    import('./app/features/user-profile/user-profile.component').then(
                        (c) => c.UserProfileComponent,
                    ),
            },
        ],
    },
    { path: '*', pathMatch: 'full', redirectTo: 'welcome' },
];
