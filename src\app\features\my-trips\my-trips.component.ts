import { CommonModule } from '@angular/common';
import { Component, OnInit, inject, input } from '@angular/core';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ToastModule } from 'primeng/toast';
import {
    Order,
    OrderService,
    OrderStatus,
    Trip,
} from '../../services/order.service';

@Component({
    selector: 'app-my-trips',
    standalone: true,
    imports: [
        CommonModule,
        CardModule,
        ButtonModule,
        ToastModule,
        ConfirmDialogModule,
    ],
    templateUrl: './my-trips.component.html',
    providers: [DialogService, MessageService, ConfirmationService],
})
export class MyTripsComponent implements OnInit {
    private orderService = inject(OrderService);
    confirmationService = inject(ConfirmationService);
    id = input.required<string>();

    order!: Order;
    private dialogRef: DynamicDialogRef | undefined;

    ngOnInit(): void {
        this.loadOrders();
    }

    loadOrders(): void {
        this.orderService.getTripById(this.id()).subscribe((response) => {
            if (response.data) {
                console.log(response.data);
            }
        });
    }

    viewTripDetails(order: Order): void {
        if (order.tripId) {
            this.orderService
                .getTripById(order.tripId)
                .subscribe((response) => {
                    if (response.data) {
                    }
                });
        }
    }

    cancelOrder(order: Order): void {
        if (order.status === OrderStatus.PENDING) {
            this.confirmationService.confirm({
                message: 'Are you sure you want to cancel this trip?',
                header: 'Cancel Trip',
                icon: 'pi pi-exclamation-triangle',
                accept: () => {
                    // ToDo;
                    // this.orderService.
                },
            });
        }
    }

    formatDate(dateString: string): string {
        const date = new Date(dateString);
        return date.toLocaleString();
    }

    ngOnDestroy(): void {
        if (this.dialogRef) {
            this.dialogRef.close();
        }
    }
}
