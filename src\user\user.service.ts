import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { DriverStatus, User } from '@prisma/client';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { SignUpDto } from './dto/signup.dto';
import { SignInDto } from './dto/signin.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import * as bcrypt from 'bcrypt';
import { randomBytes } from 'crypto';
import { UltraMsgService } from 'src/common/ultramsg/ultramsg.service';

@Injectable()
export class UserService {
  constructor(
    private prismaService: PrismaService,
    private jwtService: JwtService,
    private ultransgService: UltraMsgService,
  ) {}

  async signUp(dto: SignUpDto) {
    const { first_name, last_name, phone_number, password } = dto;

    // First check for verified user
    const existingVerifiedUser = await this.prismaService.user.findFirst({
      where: {
        phoneNumber: phone_number,
        isPhoneVerified: true,
      },
    });

    if (existingVerifiedUser) {
      throw new BadRequestException(
        'User with this phone number already exists',
      );
    }

    const salt = await bcrypt.genSalt(5);
    const hashedPassword = await bcrypt.hash(password, salt);

    const existingUnverifiedUser = await this.prismaService.user.findFirst({
      where: {
        phoneNumber: phone_number,
        isPhoneVerified: false,
      },
    });

    let user;
    if (existingUnverifiedUser) {
      user = existingUnverifiedUser;
    } else {
      // Create new user if none exists
      user = await this.prismaService.user.create({
        data: {
          firstName: first_name,
          lastName: last_name,
          phoneNumber: phone_number,
          password: hashedPassword,
          isPhoneVerified: false,
        },
      });
    }

    // Send verification code
    try {
      await this.ultransgService.sendVerificationCode(phone_number);
    } catch (error) {
      console.error('Failed to send verification code:', error);
      // Don't throw error here as user is already created
      // Just log the error and continue
    }

    return user;
  }

  async logIn(dto: SignInDto) {
    const { phone_number, password } = dto;

    const user = await this.prismaService.user.findUnique({
      where: { phoneNumber: phone_number, isPhoneVerified: true },
    });

    if (!user) {
      throw new BadRequestException('Invalid phone number or password');
    }

    const passwordMatches = await bcrypt.compare(password, user.password);
    if (!passwordMatches) {
      throw new BadRequestException('Invalid phone number or password'); // Changed from 'Invalid email or password'
    }

    const tokens = this.generateTokens(user);
    await this.saveRefreshToken(user.id, tokens.refresh_token);

    return tokens;
  }

  async verifyPhone(phoneNumber: string, code: string) {
    const user = await this.prismaService.user.findUnique({
      where: { phoneNumber },
    });

    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (user.isPhoneVerified) {
      throw new BadRequestException('Phone number already verified');
    }

    if (!user.verificationCode) {
      throw new BadRequestException('No verification code found');
    }

    if (user.verificationCode !== code) {
      throw new BadRequestException('Invalid verification code');
    }

    const currentTime = new Date();
    const codeSentAt = user.verificationCodeSentAt;
    const expirationTime = new Date(codeSentAt.getTime() + 5 * 60 * 1000); // 5 minutes

    if (currentTime > expirationTime) {
      throw new BadRequestException('Verification code has expired');
    }

    const updatedUser = await this.prismaService.user.update({
      where: { id: user.id },
      data: {
        isPhoneVerified: true,
        verificationCode: null,
      },
    });

    const tokens = this.generateTokens(updatedUser);
    await this.saveRefreshToken(updatedUser.id, tokens.refresh_token);

    return tokens;
  }

  private generateTokens(user: Partial<User>) {
    const { id, phoneNumber, isPhoneVerified, driverStatus } = user;
    const payload = { id, phoneNumber, isPhoneVerified, driverStatus };

    return {
      access_token: this.jwtService.sign(payload, { expiresIn: '2d' }),
      refresh_token: this.generateRefreshToken(),
    };
  }

  private generateRefreshToken(): string {
    return randomBytes(40).toString('hex');
  }

  private async saveRefreshToken(userId: string, token: string) {
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 100); // 100 days expiration

    await this.prismaService.refreshToken.create({
      data: {
        token,
        userId,
        expiresAt,
      },
    });
  }

  async getUserById(userId: string) {
    return this.prismaService.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        phoneNumber: true,
        firstName: true,
        lastName: true,
        driverStatus: true,
        isPhoneVerified: true,
        IdCardFrontUrl: true,
        IdCardBackUrl: true,
        PersonalPhotoUrl: true,
        car: {
          select: {
            id: true,
            make: true,
            model: true,
            year: true,
            licensePlate: true,
            photos: {
              select: {
                id: true,
                photoUrl: true,
              },
            },
          },
        },
      },
    });
  }

  async getUsers(driverStatus?: DriverStatus) {
    return this.prismaService.user.findMany({
      where: driverStatus ? { driverStatus } : undefined,
      select: {
        id: true,
        phoneNumber: true,
        firstName: true,
        lastName: true,
        driverStatus: true,
        isPhoneVerified: true,
        IdCardFrontUrl: true,
        IdCardBackUrl: true,
        PersonalPhotoUrl: true,
        car: {
          select: {
            make: true,
            model: true,
            year: true,
            licensePlate: true,
            photos: true,
          },
        },
      },
    });
  }

  async refresh(dto: RefreshTokenDto) {
    const { refresh_token } = dto;

    const refreshTokenData = await this.prismaService.refreshToken.findUnique({
      where: { token: refresh_token },
      include: { user: true },
    });

    if (!refreshTokenData) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    if (refreshTokenData.expiresAt < new Date()) {
      await this.prismaService.refreshToken.delete({
        where: { id: refreshTokenData.id },
      });
      throw new UnauthorizedException('Refresh token expired');
    }

    await this.prismaService.refreshToken.delete({
      where: { id: refreshTokenData.id },
    });

    const tokens = this.generateTokens(refreshTokenData.user);
    await this.saveRefreshToken(refreshTokenData.user.id, tokens.refresh_token);

    return tokens;
  }
}
