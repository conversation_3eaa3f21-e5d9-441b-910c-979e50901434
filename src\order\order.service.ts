import {
  Injectable,
  BadRequestException,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { Order, OrderStatus, Trip, TripStatus } from '@prisma/client';
import { CreateOrderDto } from './dto';
import { PricingService, OrderWithPoints } from '../pricing/pricing.service';

@Injectable()
export class OrderService {
  private readonly MAX_DISTANCE_KM = 5; // 5 kilometers radius
  private readonly MAX_NEARBY_ORDERS = 3;
  private readonly SUGGESTION_TIMEOUT_MINUTES = 5; // Orders suggested more than 5 minutes ago can be suggested again
  private readonly COMPANY_COMMISSION_RATE = 0.05; // 5% commission rate (configurable)

  constructor(
    private prisma: PrismaService,
    private pricingService: PricingService,
  ) {}

  async getInitialOrderPrice(data: CreateOrderDto): Promise<number> {
    // Create temporary point objects for pricing calculation (not persisted)
    const tempPickupPoint = {
      id: 'temp-pickup',
      latitude: data.pickupLatitude,
      longitude: data.pickupLongitude,
    };

    const tempDropoffPoint = {
      id: 'temp-dropoff',
      latitude: data.dropoffLatitude,
      longitude: data.dropoffLongitude,
    };

    // Create mock order object for pricing calculation
    const mockOrderForPricing = {
      pickupPoint: tempPickupPoint,
      dropoffPoint: tempDropoffPoint,
    };

    try {
      // Calculate initial price using the pricing service
      const initialPrice = await this.pricingService.calculateInitialPrice(
        mockOrderForPricing as OrderWithPoints,
      );

      return initialPrice;
    } catch (error) {
      console.error('Price calculation failed:', error);
      throw new BadRequestException('Failed to calculate price');
    }
  }

  async create(userId: string, data: CreateOrderDto): Promise<Order> {
    // Check if user has any non-completed orders
    const existingOrders = await this.prisma.order.findFirst({
      where: {
        userId,
        status: {
          in: [
            OrderStatus.PENDING,
            OrderStatus.SUGGESTED_FOR_DRIVER,
            OrderStatus.CONFIRMED,
          ],
        },
      },
    });
    console.log(existingOrders);

    if (existingOrders) {
      throw new BadRequestException(
        'You have pending orders that need to be completed first',
      );
    }

    try {
      const pickupPoint = await this.prisma.point.create({
        data: {
          latitude: data.pickupLatitude,
          longitude: data.pickupLongitude,
        },
      });

      const dropoffPoint = await this.prisma.point.create({
        data: {
          latitude: data.dropoffLatitude,
          longitude: data.dropoffLongitude,
        },
      });

      // Calculate initial price BEFORE creating the order
      const mockOrderForPricing = {
        pickupPoint: {
          id: pickupPoint.id,
          latitude: pickupPoint.latitude,
          longitude: pickupPoint.longitude,
        },
        dropoffPoint: {
          id: dropoffPoint.id,
          latitude: dropoffPoint.latitude,
          longitude: dropoffPoint.longitude,
        },
      };

      const initialPrice = await this.pricingService.calculateInitialPrice(
        mockOrderForPricing as OrderWithPoints,
      );

      // Create the order with initial price included
      const order = await this.prisma.order.create({
        data: {
          userId,
          status: OrderStatus.PENDING,
          pickupPointId: pickupPoint.id,
          dropoffPointId: dropoffPoint.id,
          initialPrice: initialPrice,
        },
        include: {
          pickupPoint: true,
          dropoffPoint: true,
        },
      });

      return order;
    } catch (error) {
      console.error('Order creation failed:', error);
      throw new BadRequestException('Failed to create order');
    }
  }

  async findNearbyPendingOrders(
    driverId: string,
    driverLat: number,
    driverLng: number,
  ): Promise<Order[]> {
    const timeoutThreshold = new Date();
    timeoutThreshold.setMinutes(
      timeoutThreshold.getMinutes() - this.SUGGESTION_TIMEOUT_MINUTES,
    );

    // Get all pending orders and timed out suggested orders with their pickup and dropoff points
    const availableOrders = await this.prisma.order.findMany({
      where: {
        AND: {
          userId: {
            not: driverId,
          },
          OR: [
            { status: OrderStatus.PENDING },
            {
              AND: [
                { status: OrderStatus.SUGGESTED_FOR_DRIVER },
                {
                  OR: [
                    { lastSuggestedAt: { gt: timeoutThreshold } },
                    { lastSuggestedAt: null },
                  ],
                },
              ],
            },
          ],
        },
      },
      include: {
        pickupPoint: true,
        dropoffPoint: true,
      },
    });

    // Calculate distances and store orders with their distances
    const ordersWithDistances = availableOrders
      .map((order) => {
        let pickupLat: number | null = null;
        let pickupLng: number | null = null;

        if (order.pickupPoint) {
          pickupLat = order.pickupPoint.latitude;
          pickupLng = order.pickupPoint.longitude;
        }

        if (!pickupLat || !pickupLng) {
          return null;
        }

        const distance = this.calculateDistance(
          driverLat,
          driverLng,
          pickupLat,
          pickupLng,
        );

        return { order, distance };
      })
      .filter((item) => item !== null && item.distance <= this.MAX_DISTANCE_KM)
      .sort((a, b) => a!.distance - b!.distance)
      .slice(0, this.MAX_NEARBY_ORDERS);

    // Update status for selected orders and return them
    const updatedOrders = await Promise.all(
      ordersWithDistances.map(async (item) => {
        return this.prisma.order.update({
          where: { id: item!.order.id },
          data: {
            status: OrderStatus.SUGGESTED_FOR_DRIVER,
            lastSuggestedAt: new Date(),
            lastSuggestedDriverId: driverId,
          },
          include: {
            pickupPoint: true,
            dropoffPoint: true,
          },
        });
      }),
    );

    return updatedOrders;
  }

  private calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;

    return distance;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  async approveOrder(orderId: string, driverId: string): Promise<Order> {
    // Get the order and verify it exists
    const order = await this.prisma.order.findUnique({
      where: { id: orderId },
      include: {
        pickupPoint: true,
        dropoffPoint: true,
      },
    });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    // Verify order status is SUGGESTED_FOR_DRIVER
    if (order.status !== OrderStatus.SUGGESTED_FOR_DRIVER) {
      throw new ConflictException('Order is not available for approval');
    }

    // Use transaction to ensure both order and trip are created atomically
    return this.prisma.$transaction(async (tx) => {
      // Create new trip with order relation
      const trip = await tx.trip.create({
        data: {
          driverId,
          status: TripStatus.DRIVER_DIDNT_ARRIVE,
          order: {
            connect: {
              id: orderId,
            },
          },
        },
      });

      // Update order status
      const updatedOrder = await tx.order.update({
        where: { id: orderId },
        data: {
          status: OrderStatus.CONFIRMED,
        },
        include: {
          trip: true,
        },
      });

      return updatedOrder;
    });
  }

  async markDriverArrived(tripId: string, driverId: string): Promise<Trip> {
    const trip = await this.prisma.trip.findUnique({
      where: { id: tripId },
      include: { order: true },
    });

    if (!trip) {
      throw new NotFoundException('Trip not found');
    }

    if (trip.driverId !== driverId) {
      throw new ConflictException('You are not assigned to this trip');
    }

    if (trip.status !== TripStatus.DRIVER_DIDNT_ARRIVE) {
      throw new ConflictException('Invalid trip status for this operation');
    }

    return this.prisma.trip.update({
      where: { id: tripId },
      data: {
        status: TripStatus.DRIVER_WAITING_CLIENT,
      },
      include: { order: true },
    });
  }

  async startTrip(tripId: string, driverId: string): Promise<Trip> {
    const trip = await this.prisma.trip.findUnique({
      where: { id: tripId },
      include: { order: true },
    });

    if (!trip) {
      throw new NotFoundException('Trip not found');
    }

    if (trip.driverId !== driverId) {
      throw new ConflictException('You are not assigned to this trip');
    }

    if (trip.status !== TripStatus.DRIVER_WAITING_CLIENT) {
      throw new ConflictException('Invalid trip status for this operation');
    }

    return this.prisma.trip.update({
      where: { id: tripId },
      data: {
        status: TripStatus.DRIVER_WITH_CLIENT,
      },
      include: { order: true },
    });
  }

  async updateTripLocation(
    tripId: string,
    driverId: string,
    latitude: number,
    longitude: number,
  ): Promise<Trip> {
    const trip = await this.prisma.trip.findUnique({
      where: {
        id: tripId,
        driverId,
      },
    });

    if (!trip) {
      throw new NotFoundException('Trip not found or not authorized');
    }

    if (trip.status === TripStatus.FINISHED) {
      throw new ConflictException('Cannot update location for finished trip');
    }

    return this.prisma.trip.update({
      where: { id: tripId },
      data: {
        currentLocationLatitude: latitude,
        currentLocationLongitude: longitude,
      },
      select: {
        id: true,
        currentLocationLatitude: true,
        currentLocationLongitude: true,
        status: true,
        createdAt: true,
        driverId: true,
      },
    });
  }

  async getTripById(tripId: string): Promise<Trip> {
    console.log('🔍 Fetching trip with ID:', tripId);

    // First, let's try a simpler query to see if the user relation works
    const trip = await this.prisma.trip.findUnique({
      where: { id: tripId },
      include: {
        order: {
          include: {
            pickupPoint: true,
            dropoffPoint: true,
            user: true, // Include all user fields
          },
        },
        driver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    if (!trip) {
      throw new NotFoundException('Trip not found');
    }

    console.log('📋 Trip found:', {
      tripId: trip.id,
      orderId: trip.order?.id,
      userId: trip.order?.userId,
      hasUser: !!trip.order?.user,
      userData: trip.order?.user,
    });

    // Let's also check if the user exists directly
    if (trip.order?.userId) {
      const user = await this.prisma.user.findUnique({
        where: { id: trip.order.userId },
      });
      console.log('👤 User check:', {
        userId: trip.order.userId,
        userExists: !!user,
        userData: user,
      });
    }

    return trip;
  }

  async completeTrip(tripId: string, driverId: string): Promise<Trip> {
    const trip = await this.prisma.trip.findUnique({
      where: { id: tripId },
      include: {
        order: {
          include: {
            user: true, // Include client for transaction
          },
        },
      },
    });

    if (!trip) {
      throw new NotFoundException('Trip not found');
    }

    if (trip.driverId !== driverId) {
      throw new ConflictException('You are not assigned to this trip');
    }

    if (trip.status !== TripStatus.DRIVER_WITH_CLIENT) {
      throw new ConflictException('Invalid trip status for this operation');
    }

    if (!trip.order) {
      throw new ConflictException('No order associated with this trip');
    }

    // Calculate final price (for now, use initial price)
    const finalPrice = trip.order.initialPrice || 0; // TODO: fix this later
    const companyCommission = finalPrice * this.COMPANY_COMMISSION_RATE;

    // Use transaction to update trip, order, and create financial transactions
    return this.prisma.$transaction(async (tx) => {
      const updatedTrip = await tx.trip.update({
        where: { id: tripId },
        data: {
          status: TripStatus.FINISHED,
        },
        include: { order: true },
      });

      // Update order with final price and completion status
      await tx.order.update({
        where: { id: trip.order.id },
        data: {
          status: OrderStatus.COMPLETED,
          finalPrice: finalPrice,
        },
      });

      // Create transaction: Client pays Driver (full amount)
      await tx.moneyTransaction.create({
        data: {
          type: 'CLIENT_TO_DRIVER',
          status: 'COMPLETED',
          amount: finalPrice,
          orderId: trip.order.id,
          fromUserId: trip.order.userId,
          toUserId: driverId,
          description: `Payment for trip ${tripId} - Client to Driver`,
        },
      });

      // Create transaction: Driver pays Company (commission)
      await tx.moneyTransaction.create({
        data: {
          type: 'DRIVER_TO_COMPANY',
          status: 'PENDING',
          amount: companyCommission,
          orderId: trip.order.id,
          fromUserId: driverId,
          toUserId: null, // Company doesn't have a user ID
          description: `Commission for trip ${tripId} - Driver to Company (${(this.COMPANY_COMMISSION_RATE * 100).toFixed(1)}%)`,
        },
      });

      return updatedTrip;
    });
  }

  async getUserOrders(userId: string): Promise<Order[]> {
    return this.prisma.order.findMany({
      where: {
        userId,
      },
      include: {
        pickupPoint: true,
        dropoffPoint: true,
        trip: {
          include: {
            driver: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                phoneNumber: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }
}
