import {
    Component,
    signal,
    computed,
    output,
    input,
    OnInit,
    AfterViewInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroupAddonModule } from 'primeng/inputgroupaddon';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { TooltipModule } from 'primeng/tooltip';
import { LatLng, latLng, MapOptions, divIcon, Marker, marker } from 'leaflet';
import { debounceTime, distinct, filter, map, switchMap } from 'rxjs';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';

import { MapComponent } from '../../shared/components/map.component';
import { NominatimService } from '../../services/nominatim.service';
import { GeolocationService } from '../../services/geolocation.service';
import { GeocodingResult } from '../../shared/types/nominatim.types';
import { injectMany } from '../../shared/helpers/injectMany';

export interface LocationSelection {
    latlng: LatLng;
    address?: string;
}

@Component({
    selector: 'app-location-picker',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ButtonModule,
        InputGroupModule,
        InputGroupAddonModule,
        AutoCompleteModule,
        TooltipModule,
        MapComponent,
    ],
    template: `
        <div class="uber-location-picker">
            <!-- Map with integrated search -->
            <div class="uber-map-container">
                <!-- Search overlay on map -->
                <div class="uber-search-overlay">
                    <div class="uber-search-input">
                        <i class="pi pi-search uber-search-icon"></i>
                        <p-autocomplete
                            [ngModel]="search()"
                            (completeMethod)="onSearchInput($event.query)"
                            [suggestions]="searchResults$() || []"
                            [optionLabel]="'display_name'"
                            (onSelect)="onSearchSelect($event.value)"
                            [placeholder]="'Search'"
                            styleClass="uber-autocomplete"
                            [appendTo]="'body'"
                        />
                        <button
                            class="uber-location-button"
                            (click)="useCurrentLocation()"
                            [title]="'Use current location'"
                        >
                            <i class="pi pi-crosshairs"></i>
                        </button>
                    </div>
                </div>

                <app-map
                    [nodes]="mapNodes()"
                    [options]="mapOptions()"
                    [fit]="fitBounds()"
                    [showLayersControl]="false"
                    (clickMap)="onMapClick($event)"
                ></app-map>
            </div>

            <!-- Selected Location Display -->
            @if (selectedLocation()) {
                <div class="uber-selected-location">
                    <div class="uber-location-info">
                        <div class="uber-location-text">
                            <h4 class="uber-location-title">
                                {{ title() }} location
                            </h4>
                            <p class="uber-location-address">
                                {{
                                    selectedLocation()!.address ||
                                        'Selected Location'
                                }}
                            </p>
                        </div>
                    </div>
                </div>
            }

            <!-- Confirm Button -->
            @if (selectedLocation()) {
                <div class="uber-confirm-container">
                    <button
                        class="uber-button uber-button-primary uber-confirm-location"
                        (click)="confirmSelection()"
                    >
                        Confirm {{ title() }}
                    </button>
                </div>
            }
        </div>
    `,
    styles: [
        `
            .uber-location-picker {
                height: 100%;
                display: flex;
                flex-direction: column;
                margin: 0;
                padding: 0;

                // Search overlay integrated with map
                .uber-search-overlay {
                    position: absolute;
                    top: 0;
                    left: 1rem;
                    right: 1rem;
                    z-index: 1000;

                    @media (max-width: 768px) {
                        left: 1rem;
                        right: 1rem;
                    }

                    .uber-search-input {
                        display: flex;
                        align-items: center;
                        background: rgba(255, 255, 255, 0.95);
                        backdrop-filter: blur(10px);
                        border: 2px solid #000000;
                        border-radius: 8px 8px 0 0;
                        padding: 0.875rem 1.125rem;
                        gap: 0.75rem;
                        transition: all 0.15s ease;
                        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                        box-sizing: border-box;

                        &:focus-within {
                            background: rgba(255, 255, 255, 1);
                            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
                        }

                        .uber-search-icon {
                            color: #666;
                            font-size: 1rem;
                        }

                        .uber-location-button {
                            background: transparent;
                            border: none;
                            color: #666;
                            cursor: pointer;
                            padding: 0.5rem;
                            border-radius: 6px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            transition: all 0.15s ease;

                            &:hover {
                                background: #000000;
                                color: #ffffff;
                            }
                        }
                    }
                }

                // Map container with integrated search
                .uber-map-container {
                    flex: 1;
                    position: relative; // For absolute positioned search overlay
                    border-radius: 0;
                    overflow: hidden;
                    border: none;
                    margin: 0;
                    padding: 0;
                    min-height: 0; // Allows flex child to shrink below content size

                    // Ensure map component takes full size
                    app-map {
                        display: block;
                        height: 100%;
                        width: 100%;

                        // Ensure Leaflet container is properly sized
                        :global(.leaflet-container) {
                            height: 100% !important;
                            width: 100% !important;
                        }

                        // Hide all Leaflet controls including layers
                        :global(.leaflet-control-layers),
                        :global(.leaflet-control-layers-toggle),
                        :global(.leaflet-control-layers-list) {
                            display: none !important;
                        }
                    }
                }

                // Map instructions
                .uber-map-instructions {
                    position: absolute;
                    bottom: 1rem;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(255, 255, 255, 0.95);
                    backdrop-filter: blur(10px);
                    padding: 0.75rem 1.25rem;
                    border-radius: 20px;
                    border: 1px solid rgba(224, 224, 224, 0.8);
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                    z-index: 999;

                    @media (max-width: 768px) {
                        bottom: 0.75rem;
                        left: 0.75rem;
                        right: 0.75rem;
                        transform: none;
                    }

                    p {
                        margin: 0;
                        color: #666;
                        font-size: 0.875rem;
                        text-align: center;

                        @media (max-width: 768px) {
                            font-size: 0.8rem;
                        }
                    }
                }

                // Selected location
                .uber-selected-location {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    background: #ffffff;
                    border: 2px solid #000000;
                    border-radius: 12px;
                    padding: 1rem;
                    margin: 1rem 1.5rem;
                    flex-shrink: 0;

                    @media (max-width: 768px) {
                        margin: 1rem;
                    }

                    .uber-location-info {
                        display: flex;
                        align-items: center;

                        .uber-location-text {
                            .uber-location-title {
                                font-size: 0.875rem;
                                font-weight: 300;
                                color: #000000;
                                margin: 0 0 0.25rem 0;
                            }

                            .uber-location-address {
                                font-size: 0.875rem;
                                font-weight: 300;
                                color: #008c45;
                                margin: 0;
                            }
                        }
                    }

                    .uber-clear-button {
                        background: transparent;
                        border: none;
                        color: #5a5a5a;
                        cursor: pointer;
                        padding: 0.5rem;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: all 0.2s ease;

                        &:hover {
                            background: rgba(0, 0, 0, 0.1);
                        }
                    }
                }

                // Confirm button
                .uber-confirm-container {
                    padding: 1rem 1.5rem;
                    flex-shrink: 0;

                    @media (max-width: 768px) {
                        padding: 1rem;
                    }

                    .uber-confirm-location {
                        width: 100%;
                        font-size: 1rem;
                        min-height: 56px;
                    }
                }

                // Button styles
                .uber-button {
                    border: none;
                    border-radius: 8px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    &.uber-button-primary {
                        background: #000;
                        color: white;

                        &:hover {
                            background: #1a1a1a;
                            transform: translateY(-1px);
                        }
                    }
                }
            }

            // Global autocomplete styling
            :global(.uber-autocomplete) {
                flex: 1;

                .p-autocomplete-input {
                    border: none !important;
                    background: transparent !important;
                    font-size: 1rem !important;
                    padding: 0 !important;

                    &:focus {
                        box-shadow: none !important;
                        outline: none !important;
                    }

                    &::placeholder {
                        color: #9e9e9e !important;
                    }
                }

                .p-autocomplete-panel {
                    border-radius: 12px !important;
                    border: 1px solid #e8e8e8 !important;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;

                    .p-autocomplete-items {
                        .p-autocomplete-item {
                            padding: 0.75rem 1rem !important;
                            border-bottom: 1px solid #f6f6f6 !important;

                            &:last-child {
                                border-bottom: none !important;
                            }

                            &:hover {
                                background: #f6f6f6 !important;
                            }

                            &.p-highlight {
                                background: #000 !important;
                                color: white !important;
                            }
                        }
                    }
                }
            }
        `,
    ],
})
export class LocationPickerComponent implements OnInit, AfterViewInit {
    services = injectMany({
        NominatimService,
        GeolocationService,
    });

    // Inputs
    title = input<string>('Location');
    placeholder = input<string>('Search for an address or click on the map');
    initialLocation = input<LatLng | null>(null);

    // Outputs
    locationSelected = output<LatLng>();

    // State
    search = signal<string>('');
    selectedLocation = signal<LocationSelection | null>(null);
    fitBounds = signal<boolean>(false);

    // Map configuration
    mapOptions = signal<MapOptions>({
        zoom: 13,
        center: latLng(33.914, 36.4444), // Default to Damascus
        zoomControl: false,
        attributionControl: false,
    });

    // Search functionality
    search$ = toObservable(this.search);
    searchResults$ = toSignal(
        this.search$.pipe(
            debounceTime(500),
            distinct(),
            filter((query) => query.length > 2),
            switchMap((query) =>
                this.services.NominatimService.forwardGeocode(query),
            ),
        ),
    );

    // Map nodes for display
    mapNodes = computed(() => {
        const selected = this.selectedLocation();
        if (!selected) return [];

        const markerInstance = marker(selected.latlng, {
            icon: divIcon({
                iconSize: [36, 36],
                className: '',
                html: `<div class="flex size-9 items-center justify-center rounded-full bg-blue-500 bg-opacity-30">
                         <div class="flex size-7 items-center justify-center rounded-full bg-blue-600 text-white">
                           <i class="pi pi-map-marker text-sm"></i>
                         </div>
                       </div>`,
            }),
        });

        return [markerInstance];
    });

    ngOnInit(): void {
        // Set initial location if provided
        const initial = this.initialLocation();
        if (initial) {
            this.selectLocation(initial);
        }
    }

    ngAfterViewInit(): void {
        // Force map resize after view initialization to ensure proper sizing
        setTimeout(() => {
            // Trigger window resize event to make Leaflet recalculate container size
            window.dispatchEvent(new Event('resize'));

            // Also directly invalidate map size if available
            const mapContainers =
                document.querySelectorAll('.leaflet-container');
            mapContainers.forEach((container) => {
                if ((container as any)._leaflet_map) {
                    (container as any)._leaflet_map.invalidateSize();
                }
            });
        }, 100);

        // Additional resize after longer timeout to handle async rendering
        setTimeout(() => {
            window.dispatchEvent(new Event('resize'));
            const mapContainers =
                document.querySelectorAll('.leaflet-container');
            mapContainers.forEach((container) => {
                if ((container as any)._leaflet_map) {
                    (container as any)._leaflet_map.invalidateSize();
                }
            });
        }, 500);
    }

    onSearchInput(query: string): void {
        this.search.set(query);
    }

    onLocationSelect(latlng: LatLng): void {
        if (latlng) {
            this.selectLocation(latlng);
        }
    }

    onSearchSelect(result: GeocodingResult): void {
        if (result && result.lat && result.lon) {
            const latlng = latLng(
                parseFloat(result.lat),
                parseFloat(result.lon),
            );
            // Just center the map on searched location, don't auto-select
            this.mapOptions.set({
                ...this.mapOptions(),
                center: latlng,
                zoom: 16,
            });
            this.search.set(result.display_name);
        }
    }

    onMapClick(event: { latlng: LatLng }): void {
        // Get address from coordinates using reverse geocoding
        this.services.NominatimService.reverseGeocode(
            event.latlng.lng,
            event.latlng.lat,
        ).subscribe({
            next: (result) => {
                if (result && result.display_name) {
                    this.selectLocation(event.latlng, result.display_name);
                } else {
                    this.selectLocation(event.latlng, 'Selected Location');
                }
            },
            error: () => {
                // Fallback to generic name if reverse geocoding fails
                this.selectLocation(event.latlng, 'Selected Location');
            },
        });
    }

    selectLocation(latlng: LatLng, address?: string): void {
        this.selectedLocation.set({ latlng, address });
        this.mapOptions.set({
            ...this.mapOptions(),
            center: latlng,
            zoom: 15,
        });
        this.fitBounds.set(true);
    }

    useCurrentLocation(): void {
        this.services.GeolocationService.getUserCountry().subscribe(
            (position) => {
                const latlng = latLng(position.latitude, position.longitude);
                // Get address for current location using reverse geocoding
                this.services.NominatimService.reverseGeocode(
                    latlng.lng,
                    latlng.lat,
                ).subscribe({
                    next: (result) => {
                        if (result && result.display_name) {
                            this.selectLocation(latlng, result.display_name);
                        } else {
                            this.selectLocation(latlng, 'Current Location');
                        }
                    },
                    error: () => {
                        // Fallback to generic name if reverse geocoding fails
                        this.selectLocation(latlng, 'Current Location');
                    },
                });
            },
        );
    }

    clearSelection(): void {
        this.selectedLocation.set(null);
        this.search.set('');
    }

    confirmSelection(): void {
        const selected = this.selectedLocation();
        if (selected) {
            this.locationSelected.emit(selected.latlng);
        }
    }
}
