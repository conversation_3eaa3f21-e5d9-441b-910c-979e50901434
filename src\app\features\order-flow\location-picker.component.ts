import {
    Component,
    signal,
    computed,
    output,
    input,
    OnInit,
    AfterViewInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroupAddonModule } from 'primeng/inputgroupaddon';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { TooltipModule } from 'primeng/tooltip';
import { LatLng, latLng, MapOptions, divIcon, Marker, marker } from 'leaflet';
import { debounceTime, distinct, filter, map, switchMap } from 'rxjs';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';

import { MapComponent } from '../../shared/components/map.component';
import { NominatimService } from '../../services/nominatim.service';
import { GeolocationService } from '../../services/geolocation.service';
import { GeocodingResult } from '../../shared/types/nominatim.types';
import { injectMany } from '../../shared/helpers/injectMany';
import { getRandomColor } from '../../shared/helpers/randomColor';

export interface LocationSelection {
    latlng: LatLng;
    address?: string;
}

@Component({
    selector: 'app-location-picker',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ButtonModule,
        InputGroupModule,
        InputGroupAddonModule,
        AutoCompleteModule,
        TooltipModule,
        MapComponent,
    ],
    template: `
        <div class="m-0 flex h-[80dvh] flex-col bg-background-color-100 p-0">
            <!-- Map with integrated search -->
            <div
                class="relative m-0 min-h-0 flex-1 rounded-none border-none p-0"
            >
                <!-- Search overlay on map -->
                <div class="absolute z-[1000] w-full">
                    <div
                        class="flex items-center gap-3 rounded-t-lg border-2 border-background-color-200 bg-white px-5 py-3.5 transition-all duration-150 focus-within:bg-background-color-100 focus-within:shadow-shadow-400"
                    >
                        <i
                            class="pi pi-search text-base text-text-color-300"
                        ></i>
                        <p-autocomplete
                            [ngModel]="search()"
                            (completeMethod)="onSearchInput($event.query)"
                            [suggestions]="searchResults$() || []"
                            [optionLabel]="'display_name'"
                            (onSelect)="onSearchSelect($event.value)"
                            [placeholder]="'Search for an address...'"
                            styleClass="w-full border-none bg-transparent text-sm text-text-color-100 placeholder:text-text-color-300 focus:outline-none"
                            [appendTo]="'body'"
                        />
                        <button
                            class="cursor-pointer rounded-md bg-black p-2 text-white transition-all duration-150 hover:scale-110"
                            (click)="useCurrentLocation()"
                            [title]="'Use current location'"
                        >
                            <i class="pi pi-map-marker text-sm"></i>
                        </button>
                    </div>
                </div>

                <app-map
                    [nodes]="mapNodes()"
                    [options]="mapOptions()"
                    [fit]="fitBounds()"
                    [showLayersControl]="false"
                    (clickMap)="onMapClick($event)"
                    class="block h-full w-full"
                ></app-map>
            </div>

            <!-- Selected Location Display -->
            @if (selectedLocation()) {
                <div
                    class="mx-4 my-4 flex flex-shrink-0 items-center justify-between rounded-xl border-2 border-background-color-200 bg-background-color-100 p-4 shadow-shadow-200 sm:mx-6"
                >
                    <div class="flex items-center gap-3">
                        <div
                            class="flex h-10 w-10 items-center justify-center rounded-full bg-main-color-600"
                        >
                            <i
                                class="pi pi-map-marker text-sm text-background-color-100"
                            ></i>
                        </div>
                        <div>
                            <h4
                                class="m-0 mb-1 text-sm font-medium text-text-color-100"
                            >
                                {{ title() }} location
                            </h4>
                            <p class="m-0 text-sm text-text-color-300">
                                {{
                                    selectedLocation()!.address ||
                                        'Selected Location'
                                }}
                            </p>
                        </div>
                    </div>
                </div>
            }

            <!-- Confirm Button -->
            @if (selectedLocation()) {
                <div class="flex-shrink-0 px-4 py-4 sm:px-6">
                    <button
                        class="flex min-h-[56px] w-full cursor-pointer items-center justify-center rounded-lg border-none bg-background-color-200 text-base font-semibold text-background-color-100 shadow-shadow-200 transition-all duration-200 hover:-translate-y-0.5 hover:bg-main-color-700 hover:shadow-shadow-400 focus:outline-none focus:ring-2 focus:ring-main-color-600 focus:ring-offset-2"
                        (click)="confirmSelection()"
                    >
                        <i class="pi pi-check mr-2 text-sm"></i>
                        Confirm {{ title() }}
                    </button>
                </div>
            }
        </div>
    `,
})
export class LocationPickerComponent implements OnInit, AfterViewInit {
    services = injectMany({
        NominatimService,
        GeolocationService,
    });

    // Inputs
    title = input<string>('Location');
    placeholder = input<string>('Search for an address or click on the map');
    initialLocation = input<LatLng | null>(null);

    // Outputs
    locationSelected = output<LatLng>();

    // State
    search = signal<string>('');
    selectedLocation = signal<LocationSelection | null>(null);
    fitBounds = signal<boolean>(false);

    // Map configuration
    mapOptions = signal<MapOptions>({});

    // Search functionality
    search$ = toObservable(this.search);
    searchResults$ = toSignal(
        this.search$.pipe(
            debounceTime(500),
            distinct(),
            filter((query) => query.length > 2),
            switchMap((query) =>
                this.services.NominatimService.forwardGeocode(query),
            ),
        ),
    );

    // Map nodes for display
    mapNodes = computed(() => {
        const selected = this.selectedLocation();
        if (!selected) return [];

        const color = getRandomColor();
        const markerInstance = marker(selected.latlng, {
            icon: divIcon({
                iconSize: [36, 36],
                className: '',
                html: `<div style="background-color:${color}" class="flex size-9 items-center justify-center rounded-full bg-opacity-30 shadow-shadow-200">
                         <div style="background-color:${color}" class="flex size-7 items-center justify-center rounded-full text-background-color-100 shadow-shadow-300">
                           <i class="pi pi-map-marker text-sm"></i>
                         </div>
                       </div>`,
            }),
        });

        return [markerInstance];
    });

    ngOnInit(): void {
        // Set initial location if provided
        const initial = this.initialLocation();
        if (initial) {
            this.selectLocation(initial);
        }
    }

    ngAfterViewInit(): void {
        // Force map resize after view initialization to ensure proper sizing
        setTimeout(() => {
            // Trigger window resize event to make Leaflet recalculate container size
            window.dispatchEvent(new Event('resize'));

            // Also directly invalidate map size if available
            const mapContainers =
                document.querySelectorAll('.leaflet-container');
            mapContainers.forEach((container) => {
                if ((container as any)._leaflet_map) {
                    (container as any)._leaflet_map.invalidateSize();
                }
            });
        }, 100);

        // Additional resize after longer timeout to handle async rendering
        setTimeout(() => {
            window.dispatchEvent(new Event('resize'));
            const mapContainers =
                document.querySelectorAll('.leaflet-container');
            mapContainers.forEach((container) => {
                if ((container as any)._leaflet_map) {
                    (container as any)._leaflet_map.invalidateSize();
                }
            });
        }, 500);
    }

    onSearchInput(query: string): void {
        this.search.set(query);
    }

    onLocationSelect(latlng: LatLng): void {
        if (latlng) {
            this.selectLocation(latlng);
        }
    }

    onSearchSelect(result: GeocodingResult): void {
        if (result && result.lat && result.lon) {
            const latlng = latLng(
                parseFloat(result.lat),
                parseFloat(result.lon),
            );
            // Just center the map on searched location, don't auto-select
            this.mapOptions.set({
                ...this.mapOptions(),
                center: latlng,
                zoom: 16,
            });
            this.search.set(result.display_name);
        }
    }

    onMapClick(event: { latlng: LatLng }): void {
        // Get address from coordinates using reverse geocoding
        this.services.NominatimService.reverseGeocode(
            event.latlng.lng,
            event.latlng.lat,
        ).subscribe({
            next: (result) => {
                if (result && result.display_name) {
                    this.selectLocation(event.latlng, result.display_name);
                } else {
                    this.selectLocation(event.latlng, 'Selected Location');
                }
            },
            error: () => {
                // Fallback to generic name if reverse geocoding fails
                this.selectLocation(event.latlng, 'Selected Location');
            },
        });
    }

    selectLocation(latlng: LatLng, address?: string): void {
        this.selectedLocation.set({ latlng, address });
        this.mapOptions.set({
            ...this.mapOptions(),
            center: latlng,
            zoom: 15,
        });
        this.fitBounds.set(true);
    }

    useCurrentLocation(): void {
        this.services.GeolocationService.getUserCountry().subscribe(
            (position) => {
                const latlng = latLng(position.latitude, position.longitude);
                // Get address for current location using reverse geocoding
                this.services.NominatimService.reverseGeocode(
                    latlng.lng,
                    latlng.lat,
                ).subscribe({
                    next: (result) => {
                        if (result && result.display_name) {
                            this.selectLocation(latlng, result.display_name);
                        } else {
                            this.selectLocation(latlng, 'Current Location');
                        }
                    },
                    error: () => {
                        // Fallback to generic name if reverse geocoding fails
                        this.selectLocation(latlng, 'Current Location');
                    },
                });
            },
        );
    }

    clearSelection(): void {
        this.selectedLocation.set(null);
        this.search.set('');
    }

    confirmSelection(): void {
        const selected = this.selectedLocation();
        if (selected) {
            this.locationSelected.emit(selected.latlng);
        }
    }
}
