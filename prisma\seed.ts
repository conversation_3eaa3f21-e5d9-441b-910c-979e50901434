import { PrismaClient } from '@prisma/client';
import { hash } from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  // Clear existing data (only what we're creating)
  await prisma.carPhoto.deleteMany();
  await prisma.car.deleteMany();
  await prisma.user.deleteMany();

  console.log('🌱 Creating test users...');

  // Standard password for all test users
  const password = await hash('Test@1234', 12);

  // Create simple test users for easy testing
  const simpleTestUser = await prisma.user.create({
      data: {
      phoneNumber: '123456789',
      firstName: 'Test',
      lastName: 'User',
      password: password,
        isPhoneVerified: true,
      driverStatus: 'NONE',
    },
  });

  const simpleTestDriver = await prisma.user.create({
      data: {
      phoneNumber: '987654321',
      firstName: 'Test',
      lastName: 'Driver',
      password: password,
        isPhoneVerified: true,
        driverStatus: 'APPROVED',
        IdCardFrontUrl: 'id_front.jpeg',
        IdCardBackUrl: 'id_back.jpeg',
        PersonalPhotoUrl: 'profile.jpeg',
      },
  });

  // Create car for simple test driver
  const simpleTestCar = await prisma.car.create({
      data: {
      userId: simpleTestDriver.id,
        make: 'Toyota',
      model: 'Test',
      year: 2023,
      licensePlate: 'TEST123',
    },
  });

  // Add car photo for test driver
  await prisma.carPhoto.create({
      data: {
      carId: simpleTestCar.id,
        photoUrl: 'car.jpeg',
      },
  });

  console.log('✅ Simple test users created:');
  console.log(`📱 User: ${simpleTestUser.phoneNumber} / Password: Test@1234`);
  console.log(`🚗 Driver: ${simpleTestDriver.phoneNumber} / Password: Test@1234`);

  console.log('');
  console.log('🎉 Database seeded successfully!');
  console.log('');
  console.log('==========================================');
  console.log('🧪 TEST CREDENTIALS (2 Users Created):');
  console.log('==========================================');
  console.log('📱 Regular User:');
  console.log('   Phone: 123456789');
  console.log('   Password: Test@1234');
  console.log('');
  console.log('🚗 Driver:');
  console.log('   Phone: 987654321');
  console.log('   Password: Test@1234');
  console.log('   Status: APPROVED');
  console.log('   Car: Toyota Test (2023)');
  console.log('==========================================');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
