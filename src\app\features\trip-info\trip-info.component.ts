import { CommonModule } from '@angular/common';
import { Component, OnInit, OnD<PERSON>roy, inject, signal, computed } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { interval, Subscription, switchMap, startWith } from 'rxjs';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ToastModule } from 'primeng/toast';
import { DialogModule } from 'primeng/dialog';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ProgressBarModule } from 'primeng/progressbar';
import { ChipModule } from 'primeng/chip';
import { TooltipModule } from 'primeng/tooltip';
import { MessageService, ConfirmationService } from 'primeng/api';
import { 
    OrderService, 
    Trip, 
    TripStatus, 
    Order, 
    OrderStatus 
} from '../../services/order.service';
import { GeolocationService } from '../../services/geolocation.service';
import { UserService } from '../../services/user.service';
import { NominatimService } from '../../services/nominatim.service';
import { MapComponent } from '../../shared/components/map.component';
import { LatLng, latLng, Layer, marker, icon } from 'leaflet';

@Component({
    selector: 'app-trip-info',
    standalone: true,
    imports: [
        CommonModule,
        CardModule,
        ButtonModule,
        ProgressSpinnerModule,
        ToastModule,
        DialogModule,
        ConfirmDialogModule,
        ProgressBarModule,
        ChipModule,
        TooltipModule,
        MapComponent
    ],
    providers: [MessageService, ConfirmationService],
    template: `
        <div class="trip-info-container">
            @if (isLoading() && !trip()) {
                <div class="loading-state">
                    <p-progressSpinner strokeWidth="2" [style]="{'width': '50px', 'height': '50px'}"></p-progressSpinner>
                    <p>Loading trip information...</p>
                </div>
            } @else if (trip() && order()) {
                <!-- Special UI for DRIVER_DIDNT_ARRIVE and DRIVER_WAITING_CLIENT status -->
                @if (trip()!.status === TripStatus.DRIVER_DIDNT_ARRIVE || trip()!.status === TripStatus.DRIVER_WAITING_CLIENT) {
                    <div class="driver-didnt-arrive-container">
                        <!-- Minimalist Header -->
                        <div class="minimalist-header">
                            <h2> Order Details</h2>
                            <div class="status-badge" [ngClass]="getStatusClass()">
                                {{ getStatusText() }}
                            </div>
                        </div>
                        
                        <!-- Map Section -->
                        <div class="map-section">
                            <app-map
                                [nodes]="mapNodes()"
                                [options]="{ center: mapCenter(), zoom: mapZoom() }"
                                [showLayersControl]="false"
                                [fit]="true">
                            </app-map>
                        </div>
                        
                        <!-- Trip Info (Minimalist) -->
                        <div class="trip-info-minimal">
                            <div class="info-row">
                                <span class="label">📍 Pickup:</span>
                                <span class="value">
                                    @if (pickupAddressLoading()) {
                                        <div class="skeleton-loader">
                                            <div class="skeleton-line"></div>
                                        </div>
                                    } @else if (pickupAddress()) {
                                        {{ pickupAddress() }}
                                    } @else {
                                        <div class="skeleton-loader">
                                            <div class="skeleton-line"></div>
                                        </div>
                                    }
                                </span>
                            </div>
                            <div class="info-row">
                                <span class="label">🏁 Destination:</span>
                                <span class="value">
                                    @if (dropoffAddressLoading()) {
                                        <div class="skeleton-loader">
                                            <div class="skeleton-line"></div>
                                        </div>
                                    } @else if (dropoffAddress()) {
                                        {{ dropoffAddress() }}
                                    } @else {
                                        <div class="skeleton-loader">
                                            <div class="skeleton-line"></div>
                                        </div>
                                    }
                                </span>
                            </div>
                            @if (isDriver()) {
                                <div class="info-row">
                                    <span class="label">👤 Client:</span>
                                    <span class="value">{{ order()!.user?.firstName }} {{ order()!.user?.lastName }}</span>
                                </div>
                            } @else {
                                <div class="info-row">
                                    <span class="label">🚗 Driver:</span>
                                    <span class="value">{{ trip()!.driver?.firstName }} {{ trip()!.driver?.lastName }}</span>
                                </div>
                            }
                        </div>
                        
                        <!-- Action Buttons (Driver Only) -->
                        @if (isDriver()) {
                            <div class="action-buttons-minimal">
                                @if (trip()!.status === TripStatus.DRIVER_DIDNT_ARRIVE) {
                                    <!-- Arrive Swipe Button -->
                                    <div class="swipe-container"
                                         [class.swipe-completed]="isSwipeCompleted()"
                                         (mousedown)="startSlide($event)"
                                         (touchstart)="startSlide($event)"
                                         (mousemove)="onSlide($event)"
                                         (touchmove)="onSlide($event)"
                                         (mouseup)="endSlide($event)"
                                         (touchend)="endSlide($event)"
                                         (mouseleave)="endSlide($event)">
                                        <div class="swipe-track">
                                            <div class="swipe-progress" [style.width.%]="getSwipeProgress()"></div>
                                            <div class="swipe-target">
                                                <span class="target-text">Arrive</span>
                                                <span class="target-icon">📍</span>
                                            </div>
                                        </div>
                                        <div class="swipe-handle" 
                                             [style.left.%]="getHandlePosition()"
                                             [class.swipe-active]="isSliding()">
                                            <span class="handle-icon">🚗</span>
                                        </div>
                                        @if (isActionInProgress()) {
                                            <div class="swipe-loading">
                                                <p-progressSpinner [style]="{'width': '20px', 'height': '20px'}"></p-progressSpinner>
                                            </div>
                                        }
                                    </div>
                                } @else if (trip()!.status === TripStatus.DRIVER_WAITING_CLIENT) {
                                    <!-- Client Packed Swipe Button -->
                                    <div class="swipe-container"
                                         [class.swipe-completed]="isSwipeCompleted()"
                                         (mousedown)="startSlide($event)"
                                         (touchstart)="startSlide($event)"
                                         (mousemove)="onSlide($event)"
                                         (touchmove)="onSlide($event)"
                                         (mouseup)="endSlide($event)"
                                         (touchend)="endSlide($event)"
                                         (mouseleave)="endSlide($event)">
                                        <div class="swipe-track">
                                            <div class="swipe-progress" [style.width.%]="getSwipeProgress()"></div>
                                            <div class="swipe-target">
                                                <span class="target-text">Client Packed</span>

                                            </div>
                                        </div>
                                        <div class="swipe-handle" 
                                             [style.left.%]="getHandlePosition()"
                                             [class.swipe-active]="isSliding()">
                                            <span class="handle-icon">👤</span>
                                        </div>
                                        @if (isActionInProgress()) {
                                            <div class="swipe-loading">
                                                <p-progressSpinner [style]="{'width': '20px', 'height': '20px'}"></p-progressSpinner>
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                        }
                    </div>
                } @else {
                    <!-- Regular Trip UI for other statuses -->
                <div class="trip-card">
                    <!-- Header -->
                    <div class="trip-header">
                        <div class="trip-title">
                            <h1>Trip #{{trip()!.id.substring(0, 8)}}</h1>
                            <div class="status-badge" [ngClass]="getStatusClass()">
                                {{ getStatusText() }}
                            </div>
                        </div>
                        <div class="trip-meta">
                            <span>{{ formatDate(trip()!.createdAt) }}</span>
                        </div>
                    </div>

                    <!-- Trip Progress -->
                    <div class="trip-progress">
                        <div class="progress-header">
                            <h2>Trip Progress</h2>
                            <p-progressBar 
                                [value]="getProgressPercentage()" 
                                [showValue]="true"
                                styleClass="custom-progress-bar">
                            </p-progressBar>
                        </div>
                        <div class="progress-steps">
                            @for (step of progressSteps(); track step.key) {
                                <div class="progress-step" [ngClass]="step.className">
                                    <div class="step-icon">{{ step.icon }}</div>
                                    <div class="step-text">{{ step.text }}</div>
                                    @if (step.timestamp) {
                                        <div class="step-time">{{ formatTime(step.timestamp) }}</div>
                                    }
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Trip Stats -->
                    <div class="trip-stats">
                        <div class="stat-item">
                            <div class="stat-icon">⏱️</div>
                            <div class="stat-details">
                                <h4>Duration</h4>
                                <p>{{ getTripDuration() }}</p>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">📏</div>
                            <div class="stat-details">
                                <h4>Distance</h4>
                                <p>{{ getEstimatedDistance() }}</p>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">💰</div>
                            <div class="stat-details">
                                <h4>Fare</h4>
                                <p>{{ getEstimatedFare() }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Route Information -->
                    <div class="route-info">
                        <div class="location-item pickup">
                            <div class="location-icon">📍</div>
                            <div class="location-details">
                                <h3>Pickup Location</h3>
                                <p>
                                    @if (pickupAddressLoading()) {
                                        <div class="skeleton-loader">
                                            <div class="skeleton-line"></div>
                                        </div>
                                    } @else if (pickupAddress()) {
                                        {{ pickupAddress() }}
                                    } @else {
                                        <div class="skeleton-loader">
                                            <div class="skeleton-line"></div>
                                        </div>
                                    }
                                </p>
                            </div>
                        </div>
                        <div class="route-line">
                            <div class="route-progress" [style.height.%]="getProgressPercentage()"></div>
                        </div>
                        <div class="location-item dropoff">
                            <div class="location-icon">🏁</div>
                            <div class="location-details">
                                <h3>Destination</h3>
                                <p>
                                    @if (dropoffAddressLoading()) {
                                        <div class="skeleton-loader">
                                            <div class="skeleton-line"></div>
                            </div>
                                    } @else if (dropoffAddress()) {
                                        {{ dropoffAddress() }}
                                    } @else {
                                        <div class="skeleton-loader">
                                            <div class="skeleton-line"></div>
                                        </div>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Participants -->
                    <div class="participants">
                        @if (isDriver()) {
                            <!-- Show client info to driver -->
                        <div class="participant client-card">
                            <div class="participant-avatar">
                                <div class="avatar-icon">👤</div>
                            </div>
                            <div class="participant-details">
                                <h3>Client</h3>
                                <p class="name">{{ order()!.user?.firstName }} {{ order()!.user?.lastName }}</p>
                                <div class="contact-actions">
                                        <button pButton 
                                                icon="pi pi-phone" 
                                                class="contact-btn"
                                                [pTooltip]="'Call Client'"
                                                (click)="callClient()">
                                        </button>
                                        <button pButton 
                                                icon="pi pi-comment" 
                                                class="contact-btn"
                                                [pTooltip]="'Message Client'"
                                                (click)="messageClient()">
                                        </button>
                                </div>
                            </div>
                        </div>
                        } @else {
                            <!-- Show driver info to client -->
                        <div class="participant driver-card">
                            <div class="participant-avatar">
                                <div class="avatar-icon">🚗</div>
                            </div>
                            <div class="participant-details">
                                <h3>Driver</h3>
                                <p class="name">{{ trip()!.driver?.firstName }} {{ trip()!.driver?.lastName }}</p>
                                @if (trip()!.driver?.car) {
                                    <div class="car-details">
                                        <p class="car-info">{{ trip()!.driver?.car?.make }} {{ trip()!.driver?.car?.model }}</p>
                                        <p class="car-year">({{ trip()!.driver?.car?.year }})</p>
                                        @if (trip()!.driver?.car?.licensePlate) {
                                            <div class="license-plate">{{ trip()!.driver?.car?.licensePlate }}</div>
                                        }
                                    </div>
                                }
                                <div class="contact-actions">
                                        <button pButton 
                                                icon="pi pi-phone" 
                                                class="contact-btn"
                                                [pTooltip]="'Call Driver'"
                                                (click)="callDriver()">
                                        </button>
                                        <button pButton 
                                                icon="pi pi-comment" 
                                                class="contact-btn"
                                                [pTooltip]="'Message Driver'"
                                                (click)="messageDriver()">
                                        </button>
                                </div>
                            </div>
                        </div>
                        }
                    </div>

                    <!-- Current Status Message -->
                    <div class="status-message" [ngClass]="getStatusMessageClass()">
                        <div class="status-content">
                            @if (isActionInProgress()) {
                                <p-progressSpinner strokeWidth="2" [style]="{'width': '20px', 'height': '20px'}"></p-progressSpinner>
                            }
                            <div class="status-text">
                                <h3>{{ getStatusTitle() }}</h3>
                                <p>{{ getStatusDescription() }}</p>
                                @if (trip()!.currentLocationLatitude && trip()!.currentLocationLongitude && isDriver()) {
                                    <p class="location-info">
                                        📍 Current location: {{ trip()!.currentLocationLatitude!.toFixed(6) }}, {{ trip()!.currentLocationLongitude!.toFixed(6) }}
                                    </p>
                                }
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons (Driver Only) -->
                    @if (isDriver() && canPerformAction()) {
                        <div class="action-buttons">
                            @switch (trip()!.status) {
                                @case (TripStatus.DRIVER_DIDNT_ARRIVE) {
                                    <button 
                                        pButton 
                                        label="✓ Mark as Arrived" 
                                        icon="pi pi-map-marker"
                                        class="action-btn primary"
                                        [loading]="isActionInProgress()"
                                        (click)="confirmMarkArrived()">
                                    </button>
                                    <button 
                                        pButton 
                                        label="📍 Update Location" 
                                        icon="pi pi-refresh"
                                        class="action-btn secondary"
                                        [loading]="isUpdatingLocation()"
                                        (click)="updateLocation()">
                                    </button>
                                }
                                @case (TripStatus.DRIVER_WAITING_CLIENT) {
                                    <button 
                                        pButton 
                                        label="🚀 Start Trip" 
                                        icon="pi pi-play"
                                        class="action-btn primary pulse"
                                        [loading]="isActionInProgress()"
                                        (click)="confirmStartTrip()">
                                    </button>
                                }
                                @case (TripStatus.DRIVER_WITH_CLIENT) {
                                    <button 
                                        pButton 
                                        label="🏁 Complete Trip" 
                                        icon="pi pi-check"
                                        class="action-btn primary"
                                        [loading]="isActionInProgress()"
                                        (click)="confirmCompleteTrip()">
                                    </button>
                                    <button 
                                        pButton 
                                        label="📍 Update Location" 
                                        icon="pi pi-refresh"
                                        class="action-btn secondary"
                                        [loading]="isUpdatingLocation()"
                                        (click)="updateLocation()">
                                    </button>
                                }
                            }
                        </div>
                    }

                </div>
                }
            } @else {
                <div class="error-state">
                    <h2>Trip Not Found</h2>
                    <p>The requested trip could not be found or you don't have permission to view it.</p>
                    <button pButton label="Go Back" class="action-btn secondary" (click)="goBack()"></button>
                </div>
            }
        </div>

        <p-toast position="top-right"></p-toast>
        <p-confirmDialog 
            [style]="{width: '90vw', maxWidth: '500px'}"
            [baseZIndex]="10000"
            rejectButtonStyleClass="p-button-outlined"
            acceptButtonStyleClass="p-button-danger">
        </p-confirmDialog>
    `,
    styles: [`
        .trip-info-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .loading-state, .error-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 60px 20px;
            color: #333;
            animation: fadeIn 0.5s ease-in;
        }

        .loading-state p, .error-state p {
            margin-top: 20px;
            color: #666;
        }

        .trip-card {
            background: #ffffff;
            border: 2px solid #000000;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideInUp {
            from { 
                opacity: 0; 
                transform: translateY(30px); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0); 
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .trip-header {
            background: linear-gradient(135deg, #000000 0%, #333333 100%);
            color: #ffffff;
            padding: 25px;
            position: relative;
            overflow: hidden;
        }

        .trip-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 48%, rgba(255,255,255,0.1) 50%, transparent 52%);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .trip-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .trip-title h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 0;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-badge.en-route {
            background: #ffffff;
            color: #000000;
        }

        .status-badge.waiting {
            background: #f0f0f0;
            color: #000000;
        }

        .status-badge.in-progress {
            background: #ffffff;
            color: #000000;
            animation: pulse 2s infinite;
        }

        .status-badge.completed {
            background: #f0f0f0;
            color: #000000;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .trip-meta {
            color: #cccccc;
            font-size: 14px;
        }

        .trip-progress {
            padding: 30px 20px;
            border-bottom: 1px solid #e0e0e0;
            background: linear-gradient(90deg, #fafafa 0%, #ffffff 100%);
        }

        .progress-header {
            margin-bottom: 25px;
        }

        .progress-header h2 {
            margin: 0 0 15px 0;
            font-size: 20px;
            font-weight: 600;
            color: #000000;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            position: relative;
            margin-top: 20px;
        }

        .progress-steps::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            height: 3px;
            background: linear-gradient(90deg, #e0e0e0 0%, #f0f0f0 100%);
            z-index: 1;
            border-radius: 2px;
        }

        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            flex: 1;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .step-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            margin-bottom: 10px;
            border: 3px solid #e0e0e0;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .step-text {
            font-size: 13px;
            color: #666;
            max-width: 90px;
            font-weight: 500;
            line-height: 1.2;
        }

        .step-time {
            font-size: 11px;
            color: #999;
            margin-top: 4px;
        }

        .progress-step.completed .step-icon {
            background: linear-gradient(135deg, #000000 0%, #333333 100%);
            color: #ffffff;
            border-color: #000000;
            transform: scale(1.1);
        }

        .progress-step.active .step-icon {
            background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
            color: #000000;
            border-color: #000000;
            box-shadow: 0 0 0 3px #ffffff, 0 0 0 6px #000000, 0 4px 12px rgba(0,0,0,0.15);
            animation: pulse 2s infinite;
            transform: scale(1.15);
        }

        .progress-step.completed .step-text,
        .progress-step.active .step-text {
            color: #000000;
            font-weight: 600;
        }

        .trip-stats {
            padding: 25px 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #fafafa;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            background: #ffffff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: transform 0.2s ease;
        }

        .stat-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .stat-icon {
            font-size: 24px;
            margin-right: 12px;
        }

        .stat-details h4 {
            margin: 0 0 4px 0;
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-details p {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #000000;
        }

        .route-info {
            padding: 30px 20px;
            border-bottom: 1px solid #e0e0e0;
            position: relative;
            background: #ffffff;
        }

        .location-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 30px;
            padding: 15px;
            background: #fafafa;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .location-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .location-item:last-child {
            margin-bottom: 0;
        }

        .location-icon {
            font-size: 24px;
            margin-right: 15px;
            margin-top: 5px;
        }

        .location-details h3 {
            margin: 0 0 8px 0;
            font-size: 18px;
            font-weight: 600;
            color: #000000;
        }

        .location-details p {
            margin: 0 0 5px 0;
            color: #333;
            font-size: 15px;
            line-height: 1.4;
        }

        .coordinates {
            font-family: monospace;
            font-size: 12px;
            color: #666;
            background: #f0f0f0;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
        }

        .route-line {
            position: absolute;
            left: 50px;
            top: 90px;
            bottom: 90px;
            width: 4px;
            background: linear-gradient(180deg, #e0e0e0 0%, #000000 100%);
            border-radius: 2px;
            overflow: hidden;
        }

        .route-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background: linear-gradient(180deg, #000000 0%, #333333 100%);
            border-radius: 2px;
            transition: height 0.8s ease;
        }

        .participants {
            padding: 30px 20px;
            border-bottom: 1px solid #e0e0e0;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            background: #fafafa;
        }

        .participant {
            background: #ffffff;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .participant::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #000000, #333333);
        }

        .participant:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }

        .client-card::before {
            background: linear-gradient(90deg, #4facfe, #00f2fe);
        }

        .driver-card::before {
            background: linear-gradient(90deg, #43e97b, #38f9d7);
        }

        .participant-avatar {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            position: relative;
        }

        .avatar-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-right: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .participant-details h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            font-weight: 600;
            color: #000000;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .participant-details .name {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 18px;
            font-weight: 600;
        }

        .participant-details .phone {
            margin: 0 0 15px 0;
            color: #000000;
            font-size: 14px;
            font-weight: 600;
            font-family: monospace;
        }

        .car-details {
            margin: 10px 0;
            padding: 10px;
            background: #f8f8f8;
            border-radius: 6px;
        }

        .car-info {
            margin: 0 0 4px 0;
            font-weight: 600;
            color: #333;
        }

        .car-year {
            margin: 0 0 8px 0;
            color: #666;
            font-style: italic;
        }

        .license-plate {
            background: linear-gradient(135deg, #000000, #333333);
            color: #ffffff;
            padding: 6px 12px;
            border-radius: 4px;
            font-weight: 600;
            font-size: 14px;
            letter-spacing: 1px;
            display: inline-block;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .contact-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .contact-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f0f0f0;
            border: 2px solid #e0e0e0;
            color: #666;
            transition: all 0.2s ease;
        }

        .contact-btn:hover {
            background: #000000;
            color: #ffffff;
            border-color: #000000;
            transform: scale(1.1);
        }

        .status-message {
            padding: 30px 20px;
            border-bottom: 1px solid #000000;
        }

        .status-content {
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .status-text h3 {
            margin: 0 0 8px 0;
            font-size: 18px;
            font-weight: 600;
            color: #000000;
        }

        .status-text p {
            margin: 0 0 5px 0;
            color: #666;
            line-height: 1.5;
        }

        .location-info {
            font-family: monospace;
            font-size: 12px !important;
            background: #f5f5f5;
            padding: 8px;
            margin-top: 10px;
        }

        .status-message.waiting {
            background: #f9f9f9;
        }

        .status-message.in-progress {
            background: #f5f5f5;
        }

        .action-buttons {
            padding: 30px 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            background: #fafafa;
        }

        .action-btn {
            flex: 1;
            min-width: 180px;
            height: 55px;
            border: 2px solid #000000;
            border-radius: 8px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            font-size: 14px;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .action-btn:hover::before {
            left: 100%;
        }

        .action-btn.primary {
            background: linear-gradient(135deg, #000000 0%, #333333 100%);
            color: #ffffff;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .action-btn.primary:hover:not(:disabled) {
            background: linear-gradient(135deg, #333333 0%, #555555 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0,0,0,0.4);
        }

        .action-btn.secondary {
            background: #ffffff;
            color: #000000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .action-btn.secondary:hover:not(:disabled) {
            background: #f5f5f5;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0,0,0,0.15);
        }

        .action-btn.pulse {
            animation: pulse 2s infinite;
        }

        .action-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none !important;
        }

        .refresh-info {
            padding: 20px;
            background: linear-gradient(135deg, #f8f8f8 0%, #e8e8e8 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #e0e0e0;
        }

        ::ng-deep .custom-progress-bar .p-progressbar-value {
            background: linear-gradient(90deg, #000000, #333333) !important;
        }

        ::ng-deep .user-chip {
            background: linear-gradient(135deg, #000000, #333333) !important;
            color: white !important;
            font-size: 11px !important;
            padding: 4px 8px !important;
        }

        @media (max-width: 768px) {
            .trip-info-container {
                padding: 10px;
                background: #ffffff;
            }

            .trip-card {
                border-radius: 8px;
            }

            .participants {
                grid-template-columns: 1fr;
            }

            .trip-stats {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .trip-title {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .action-buttons {
                flex-direction: column;
            }

            .action-btn {
                min-width: 100%;
                height: 50px;
            }

            .refresh-info {
                flex-direction: column;
                gap: 8px;
                text-align: center;
            }

            .progress-steps {
                flex-wrap: wrap;
                justify-content: center;
                gap: 15px;
            }

            .progress-steps::before {
                display: none;
            }

            .step-icon {
                width: 35px;
                height: 35px;
                font-size: 14px;
            }

            .route-line {
                display: none;
            }

            .location-item {
                transform: none !important;
            }

            .participant {
                transform: none !important;
            }
        }

        @media (max-width: 480px) {
            .trip-info-container {
                padding: 5px;
            }

            .trip-header {
                padding: 15px;
            }

            .trip-progress,
            .route-info,
            .participants,
            .action-buttons {
                padding: 20px 15px;
            }

            .stat-item {
                padding: 12px;
            }
        }

        /* Minimalist UI for DRIVER_DIDNT_ARRIVE status */
        .driver-didnt-arrive-container {
            padding: 1rem;
            max-width: 100%;
            margin: 0 auto;
        }

        .minimalist-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding: 1rem;
            background: linear-gradient(135deg, #000 0%, #333 100%);
            color: white;
            border-radius: 12px;
        }

        .minimalist-header h2 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .map-section {
            height: 400px;
            margin-bottom: 1rem;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .trip-info-minimal {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-row .label {
            font-weight: 600;
            color: #666;
            font-size: 0.9rem;
        }

        .info-row .value {
            color: #000;
            font-weight: 500;
            text-align: right;
            flex: 1;
            margin-left: 1rem;
        }

        .action-buttons-minimal {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        /* Swipe Button Styles */
        .swipe-container {
            position: relative;
            width: 100%;
            height: 60px;
            background: linear-gradient(135deg, #000 0%, #333 100%);
            border: none;
            border-radius: 30px;
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            overflow: hidden;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            user-select: none;
            touch-action: none;
        }

        .swipe-container:hover:not(:disabled) {
            background: linear-gradient(135deg, #333 0%, #555 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }

        .swipe-container:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .swipe-track {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 30px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 20px;
            box-sizing: border-box;
        }

        .swipe-progress {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: linear-gradient(90deg, #e0e0e0 0%, #cccccc 100%);
            border-radius: 30px;
            transition: width 0.3s ease-in-out;
            z-index: 1;
        }

        .swipe-target {
            position: absolute;
            top: 0;
            right: 0;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            box-sizing: border-box;
            z-index: 2;
        }

        .target-text {
            font-size: 1.1rem;
            font-weight: 600;
            color: #ffffff;
            margin-right: 8px;
        }

        .target-icon {
            font-size: 1.2rem;
        }

        .swipe-handle {
            position: absolute;
            top: 0;
            left: 0;
            width: 60px;
            height: 100%;
            border-radius: 30px;
            background: linear-gradient(135deg, #ffffff, #f0f0f0);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 3;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            transition: left 0.3s ease-in-out;
            cursor: grab;
        }

        .swipe-handle:active {
            cursor: grabbing;
        }

        .swipe-handle.swipe-active {
            background: linear-gradient(135deg, #ffffff, #e0e0e0);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transform: scale(1.05);
        }

        .swipe-container.swipe-completed .swipe-handle {
            left: calc(100% - 60px);
            background: linear-gradient(135deg, #333333, #555555);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .handle-icon {
            font-size: 1.2rem;
            color: #000000;
        }

        .swipe-loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 30px;
            z-index: 4;
        }

        .update-location-btn {
            background: white;
            border: 2px solid #000;
            color: #000;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .update-location-btn:hover:not(:disabled) {
            background: #f8f9fa;
            transform: translateY(-2px);
        }

        .update-location-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .minimalist-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .map-section {
                height: 300px;
            }

            .info-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .info-row .value {
                text-align: left;
                margin-left: 0;
            }
        }
    `]
})
export class TripInfoComponent implements OnInit, OnDestroy {
    // Services
    private route = inject(ActivatedRoute);
    private router = inject(Router);
    private orderService = inject(OrderService);
    private messageService = inject(MessageService);
    private confirmationService = inject(ConfirmationService);
    private geoService = inject(GeolocationService);
    private userService = inject(UserService);
    private nominatimService = inject(NominatimService);

    // Enums for template
    TripStatus = TripStatus;
    OrderStatus = OrderStatus;

    // Signals
    trip = signal<Trip | null>(null);
    order = signal<Order | null>(null);
    isLoading = signal<boolean>(false);
    isActionInProgress = signal<boolean>(false);
    isUpdatingLocation = signal<boolean>(false);
    lastUpdated = signal<Date>(new Date());
    currentUser = signal<any>(null);

    // Map-related signals
    mapNodes = signal<Layer[]>([]);
    mapCenter = signal<LatLng>(latLng(0, 0));
    mapZoom = signal<number>(16);

    // Location address cache
    pickupAddress = signal<string>('');
    dropoffAddress = signal<string>('');
    pickupAddressLoading = signal<boolean>(false);
    dropoffAddressLoading = signal<boolean>(false);

    // Subscriptions
    private refreshSubscription?: Subscription;
    private locationUpdateSubscription?: Subscription;

    // Computed properties
    isDriver = computed(() => {
        const user = this.currentUser();
        const trip = this.trip();
        return user && trip && user.id === trip.driverId;
    });

    progressSteps = computed(() => {
        const status = this.trip()?.status;
        const trip = this.trip();
        const createdAt = trip?.createdAt ? new Date(trip.createdAt) : null;
        
        const steps = [
            { 
                key: 'en-route', 
                icon: '🚗', 
                text: 'En Route', 
                className: '',
                timestamp: null as Date | null
            },
            { 
                key: 'waiting', 
                icon: '⏰', 
                text: 'Waiting', 
                className: '',
                timestamp: null as Date | null
            },
            { 
                key: 'in-progress', 
                icon: '🛣️', 
                text: 'In Progress', 
                className: '',
                timestamp: null as Date | null
            },
            { 
                key: 'completed', 
                icon: '✅', 
                text: 'Completed', 
                className: '',
                timestamp: null as Date | null
            }
        ];

        // Set timestamps based on trip status progression
        if (createdAt) {
            steps[0].timestamp = createdAt;
        }

        switch (status) {
            case TripStatus.DRIVER_DIDNT_ARRIVE:
                steps[0].className = 'active';
                break;
            case TripStatus.DRIVER_WAITING_CLIENT:
                steps[0].className = 'completed';
                steps[1].className = 'active';
                steps[1].timestamp = new Date(); // Approximate
                break;
            case TripStatus.DRIVER_WITH_CLIENT:
                steps[0].className = 'completed';
                steps[1].className = 'completed';
                steps[2].className = 'active';
                steps[1].timestamp = new Date(createdAt!.getTime() + 5 * 60000); // +5 min estimate
                steps[2].timestamp = new Date(createdAt!.getTime() + 10 * 60000); // +10 min estimate
                break;
            case TripStatus.FINISHED:
                steps.forEach((step, index) => {
                    step.className = 'completed';
                    if (index === 0) step.timestamp = createdAt;
                    if (index === 1) step.timestamp = new Date(createdAt!.getTime() + 5 * 60000);
                    if (index === 2) step.timestamp = new Date(createdAt!.getTime() + 10 * 60000);
                    if (index === 3) step.timestamp = new Date(createdAt!.getTime() + 30 * 60000);
                });
                break;
        }

        return steps;
    });

    ngOnInit(): void {
        this.loadCurrentUser();
        this.loadTripData();
        this.startAutoRefresh();
        this.startAutoLocationUpdate();
    }

    ngOnDestroy(): void {
        if (this.refreshSubscription) {
            this.refreshSubscription.unsubscribe();
        }
        if (this.locationUpdateSubscription) {
            this.locationUpdateSubscription.unsubscribe();
        }
    }

    private loadCurrentUser(): void {
        this.userService.getProfile().subscribe((response: any) => {
            if (response.data) {
                this.currentUser.set(response.data);
            }
        });
    }

    private loadTripData(): void {
        const tripId = this.route.snapshot.paramMap.get('id');
        if (!tripId) {
            this.router.navigate(['/main']);
            return;
        }

        this.isLoading.set(true);
        this.orderService.getTripById(tripId).subscribe({
            next: (response) => {
                if (response.data) {
                    this.trip.set(response.data);
                    // Load order data if available
                    if (response.data.order) {
                        console.log('Order data loaded:', response.data.order);
                        console.log('Pickup point:', response.data.order.pickupPoint);
                        console.log('Dropoff point:', response.data.order.dropoffPoint);
                        this.order.set(response.data.order);
                        
                        // Load addresses for pickup and dropoff points
                        this.loadLocationAddresses();
                    }
                    // Setup map when trip data is loaded
                    this.setupMap();
                }
                this.isLoading.set(false);
                this.lastUpdated.set(new Date());
            },
            error: (error) => {
                console.error('Error loading trip:', error);
                this.isLoading.set(false);
                this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to load trip information'
                });
            }
        });
    }

    private startAutoRefresh(): void {
        this.refreshSubscription = interval(10000)
            .pipe(
                startWith(0),
                switchMap(() => {
                    const tripId = this.route.snapshot.paramMap.get('id');
                    if (tripId) {
                        return this.orderService.getTripById(tripId);
                    }
                    return [];
                })
            )
            .subscribe({
                next: (response: any) => {
                    if (response.data) {
                        this.trip.set(response.data);
                        if (response.data.order) {
                            this.order.set(response.data.order);
                        }
                        this.lastUpdated.set(new Date());
                    }
                },
                error: (error) => {
                    console.error('Auto-refresh error:', error);
                }
            });
    }

    // Status helpers
    getStatusClass(): string {
        switch (this.trip()?.status) {
            case TripStatus.DRIVER_DIDNT_ARRIVE:
                return 'en-route';
            case TripStatus.DRIVER_WAITING_CLIENT:
                return 'waiting';
            case TripStatus.DRIVER_WITH_CLIENT:
                return 'in-progress';
            case TripStatus.FINISHED:
                return 'completed';
            default:
                return '';
        }
    }

    getStatusText(): string {
        switch (this.trip()?.status) {
            case TripStatus.DRIVER_DIDNT_ARRIVE:
                return 'En Route';
            case TripStatus.DRIVER_WAITING_CLIENT:
                return 'Waiting';
            case TripStatus.DRIVER_WITH_CLIENT:
                return 'In Progress';
            case TripStatus.FINISHED:
                return 'Completed';
            default:
                return 'Unknown';
        }
    }

    getStatusMessageClass(): string {
        const status = this.trip()?.status;
        if (status === TripStatus.DRIVER_WAITING_CLIENT) return 'waiting';
        if (status === TripStatus.DRIVER_WITH_CLIENT) return 'in-progress';
        return '';
    }

    getStatusTitle(): string {
        const isDriver = this.isDriver();
        switch (this.trip()?.status) {
            case TripStatus.DRIVER_DIDNT_ARRIVE:
                return isDriver ? 'Navigate to pickup location' : 'Driver is on the way';
            case TripStatus.DRIVER_WAITING_CLIENT:
                return isDriver ? 'Waiting for client' : 'Driver has arrived';
            case TripStatus.DRIVER_WITH_CLIENT:
                return isDriver ? 'Trip in progress' : 'Trip in progress';
            case TripStatus.FINISHED:
                return 'Trip completed successfully';
            default:
                return 'Trip status unknown';
        }
    }

    getStatusDescription(): string {
        const isDriver = this.isDriver();
        switch (this.trip()?.status) {
            case TripStatus.DRIVER_DIDNT_ARRIVE:
                return isDriver 
                    ? 'Navigate to the pickup location and mark as arrived when you reach the client.'
                    : 'Your driver is currently on the way to pick you up. You will be notified when they arrive.';
            case TripStatus.DRIVER_WAITING_CLIENT:
                return isDriver
                    ? 'You have arrived at the pickup location. Please wait for the client and start the trip once they are in the vehicle.'
                    : 'Your driver has arrived at the pickup location. Please head to the vehicle to begin your trip.';
            case TripStatus.DRIVER_WITH_CLIENT:
                return isDriver
                    ? 'Trip is currently in progress. Complete the trip when you reach the destination.'
                    : 'Your trip is currently in progress. Sit back and enjoy the ride!';
            case TripStatus.FINISHED:
                return 'Thank you for using our service! This trip has been completed successfully.';
            default:
                return 'Loading trip status...';
        }
    }

    canPerformAction(): boolean {
        const trip = this.trip();
        return trip && trip.status !== TripStatus.FINISHED && this.isDriver();
    }

    // Driver actions
    markArrived(): void {
        const tripId = this.trip()?.id;
        if (!tripId) return;

        this.isActionInProgress.set(true);
        this.orderService.markDriverArrived(tripId).subscribe({
            next: (response) => {
                if (response.data) {
                    this.trip.set(response.data);
                }
                this.isActionInProgress.set(false);
            },
            error: (error) => {
                console.error('Error marking arrived:', error);
                this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to mark as arrived'
                });
                this.isActionInProgress.set(false);
            }
        });
    }

    markClientPacked(): void {
        const tripId = this.trip()?.id;
        if (!tripId) return;

        this.isActionInProgress.set(true);
        this.orderService.startTrip(tripId).subscribe({
            next: (response) => {
                if (response.data) {
                    this.trip.set(response.data);
                }
                this.isActionInProgress.set(false);
            },
            error: (error) => {
                console.error('Error marking client packed:', error);
                this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to mark client as packed'
                });
                this.isActionInProgress.set(false);
            }
        });
    }

    startTrip(): void {
        const tripId = this.trip()?.id;
        if (!tripId) return;

        this.isActionInProgress.set(true);
        this.orderService.startTrip(tripId).subscribe({
            next: (response) => {
                if (response.data) {
                    this.trip.set(response.data);
                }
                this.isActionInProgress.set(false);
            },
            error: (error) => {
                console.error('Error starting trip:', error);
                this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to start trip'
                });
                this.isActionInProgress.set(false);
            }
        });
    }

    completeTrip(): void {
        const tripId = this.trip()?.id;
        if (!tripId) return;

        this.isActionInProgress.set(true);
        this.orderService.completeTrip(tripId).subscribe({
            next: (response) => {
                if (response.data) {
                    this.trip.set(response.data);
                }
                this.isActionInProgress.set(false);
            },
            error: (error) => {
                console.error('Error completing trip:', error);
                this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to complete trip'
                });
                this.isActionInProgress.set(false);
            }
        });
    }

    updateLocation(): void {
        const tripId = this.trip()?.id;
        if (!tripId) return;

        this.isUpdatingLocation.set(true);
        this.geoService.getUserCountry().subscribe({
            next: (position: any) => {
                const locationDto = {
                    latitude: position.latitude,
                    longitude: position.longitude
                };

                this.orderService.updateTripLocation(tripId, locationDto).subscribe({
                    next: (response: any) => {
                        if (response.data) {
                            this.trip.set(response.data);
                        }
                        this.isUpdatingLocation.set(false);
                    },
                    error: (error: any) => {
                        console.error('Error updating location:', error);
                        this.messageService.add({
                            severity: 'error',
                            summary: 'Error',
                            detail: 'Failed to update location'
                        });
                        this.isUpdatingLocation.set(false);
                    }
                });
            },
            error: (error: any) => {
                console.error('Error getting position:', error);
                this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to get current location'
                });
                this.isUpdatingLocation.set(false);
            }
        });
    }

    // Utility methods
    formatDate(dateString: string): string {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    formatTime(date: Date): string {
        return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    formatLocation(point: any): string {
        if (!point) return 'Location not available';
        return `${point.latitude.toFixed(6)}, ${point.longitude.toFixed(6)}`;
    }

    goBack(): void {
        this.router.navigate(['/main']);
    }

    // New enhanced functionality methods

    /**
     * Get progress percentage for progress bar
     */
    getProgressPercentage(): number {
        const status = this.trip()?.status;
        switch (status) {
            case TripStatus.DRIVER_DIDNT_ARRIVE:
                return 25;
            case TripStatus.DRIVER_WAITING_CLIENT:
                return 50;
            case TripStatus.DRIVER_WITH_CLIENT:
                return 75;
            case TripStatus.FINISHED:
                return 100;
            default:
                return 0;
        }
    }

    /**
     * Get trip duration string
     */
    getTripDuration(): string {
        const trip = this.trip();
        if (!trip?.createdAt) return 'N/A';
        
        const start = new Date(trip.createdAt);
        const now = new Date();
        const diffMs = now.getTime() - start.getTime();
        const diffMins = Math.floor(diffMs / 60000);
        
        if (diffMins < 60) {
            return `${diffMins} min`;
        } else {
            const hours = Math.floor(diffMins / 60);
            const minutes = diffMins % 60;
            return `${hours}h ${minutes}m`;
        }
    }

    /**
     * Get estimated distance (placeholder)
     */
    getEstimatedDistance(): string {
        // In a real app, this would calculate distance between pickup and dropoff
        const order = this.order();
        if (!order?.pickupPoint || !order?.dropoffPoint) return 'N/A';
        
        // Simple distance calculation (placeholder)
        const lat1 = order.pickupPoint.latitude;
        const lon1 = order.pickupPoint.longitude;
        const lat2 = order.dropoffPoint.latitude;
        const lon2 = order.dropoffPoint.longitude;
        
        const R = 6371; // Earth's radius in km
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLon = (lon2 - lon1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLon/2) * Math.sin(dLon/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        const distance = R * c;
        
        return `${distance.toFixed(1)} km`;
    }

    /**
     * Get estimated fare (placeholder)
     */
    getEstimatedFare(): string {
        // In a real app, this would calculate based on distance, time, surge pricing, etc.
        const distance = parseFloat(this.getEstimatedDistance());
        if (isNaN(distance)) return 'N/A';
        
        const baseFare = 5;
        const perKmRate = 2.5;
        const estimatedFare = baseFare + (distance * perKmRate);
        
        return `$${estimatedFare.toFixed(2)}`;
    }

    /**
     * Format location with address using reverse geocoding
     */
    formatLocationWithAddress(point: any): string {
        console.log('formatLocationWithAddress called with:', point);
        
        if (!point) {
            console.log('Point is null or undefined');
            return 'Location not available';
        }
        
        if (!point.latitude || !point.longitude) {
            console.log('Point missing latitude or longitude:', point);
            return 'Coordinates not available';
        }
        
        // Return cached address if available
        if (point.address) {
            return point.address;
        }
        
        // Return empty string to show skeleton loading
        return '';
    }

    /**
     * Load addresses for pickup and dropoff points using reverse geocoding
     */
    private loadLocationAddresses(): void {
        const order = this.order();
        if (!order) return;

        // Load pickup address
        if (order.pickupPoint && order.pickupPoint.latitude && order.pickupPoint.longitude) {
            this.pickupAddressLoading.set(true);
            this.nominatimService.reverseGeocode(order.pickupPoint.longitude, order.pickupPoint.latitude).subscribe({
                next: (result) => {
                    console.log('Pickup reverse geocoding result:', result);
                    if (result.display_name) {
                        (order.pickupPoint as any).address = result.display_name;
                        this.pickupAddress.set(result.display_name);
                    }
                    this.pickupAddressLoading.set(false);
                },
                error: (error) => {
                    console.error('Pickup reverse geocoding error:', error);
                    this.pickupAddressLoading.set(false);
                }
            });
        }

        // Load dropoff address
        if (order.dropoffPoint && order.dropoffPoint.latitude && order.dropoffPoint.longitude) {
            this.dropoffAddressLoading.set(true);
            this.nominatimService.reverseGeocode(order.dropoffPoint.longitude, order.dropoffPoint.latitude).subscribe({
                next: (result) => {
                    console.log('Dropoff reverse geocoding result:', result);
                    if (result.display_name) {
                        (order.dropoffPoint as any).address = result.display_name;
                        this.dropoffAddress.set(result.display_name);
                    }
                    this.dropoffAddressLoading.set(false);
                },
                error: (error) => {
                    console.error('Dropoff reverse geocoding error:', error);
                    this.dropoffAddressLoading.set(false);
                }
            });
        }
    }

    /**
     * Format phone number
     */
    formatPhoneNumber(phone: string | undefined): string {
        if (!phone) return 'N/A';
        
        // Basic phone number formatting
        const cleaned = phone.replace(/\D/g, '');
        if (cleaned.length === 10) {
            return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
        }
        return phone;
    }

    // Confirmation dialog methods
    confirmMarkArrived(): void {
        this.confirmationService.confirm({
            message: 'Are you sure you have arrived at the pickup location?',
            header: 'Confirm Arrival',
            icon: 'pi pi-map-marker',
            acceptLabel: 'Yes, I have arrived',
            rejectLabel: 'Cancel',
            accept: () => {
                this.markArrivedWithClientCheck();
            }
        });
    }

    markArrivedWithClientCheck(): void {
        const tripId = this.trip()?.id;
        if (!tripId) return;

        this.isActionInProgress.set(true);
        this.orderService.markDriverArrived(tripId).subscribe({
            next: (response) => {
                if (response.data) {
                    this.trip.set(response.data);
                    // Now ask if client has arrived
                    this.checkClientArrival();
                }
                this.isActionInProgress.set(false);
            },
            error: (error) => {
                console.error('Error marking arrived:', error);
                this.messageService.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to mark as arrived'
                });
                this.isActionInProgress.set(false);
            }
        });
    }

    checkClientArrival(): void {
        this.confirmationService.confirm({
            message: 'Has the client arrived at the pickup location?',
            header: 'Client Status',
            icon: 'pi pi-user',
            acceptLabel: 'Yes, client is here',
            rejectLabel: 'No, still waiting',
            accept: () => {
                // Client is here, start the trip immediately
                this.startTrip();
            },
            reject: () => {
                // Client is not here, stay in DRIVER_WAITING_CLIENT status
                this.messageService.add({
                    severity: 'info',
                    summary: 'Waiting for Client',
                    detail: 'You are now waiting for the client to arrive'
                });
            }
        });
    }

    confirmStartTrip(): void {
        this.confirmationService.confirm({
            message: 'Is the client in the vehicle and ready to start the trip?',
            header: 'Start Trip',
            icon: 'pi pi-play',
            acceptLabel: 'Yes, start trip',
            rejectLabel: 'Cancel',
            accept: () => {
                this.startTrip();
            }
        });
    }

    confirmCompleteTrip(): void {
        this.confirmationService.confirm({
            message: 'Have you reached the destination? This will complete the trip and process payment.',
            header: 'Complete Trip',
            icon: 'pi pi-check',
            acceptLabel: 'Yes, complete trip',
            rejectLabel: 'Cancel',
            accept: () => {
                this.completeTrip();
            }
        });
    }

    // Contact methods (placeholders)
    callClient(): void {
        const phone = this.order()?.user?.phoneNumber;
        if (phone) {
            // In a real app, this would initiate a call
            window.open(`tel:${phone}`);
        }
    }

    messageClient(): void {
        const phone = this.order()?.user?.phoneNumber;
        if (phone) {
            // In a real app, this would open SMS or in-app messaging
            window.open(`sms:${phone}`);
        }
    }

    callDriver(): void {
        const phone = this.trip()?.driver?.phoneNumber;
        if (phone) {
            window.open(`tel:${phone}`);
        }
    }

    messageDriver(): void {
        const phone = this.trip()?.driver?.phoneNumber;
        if (phone) {
            window.open(`sms:${phone}`);
        }
    }

    // Auto location update method
    private startAutoLocationUpdate(): void {
        if (this.isDriver()) {
            this.locationUpdateSubscription = interval(5000).subscribe(() => {
                this.updateLocation();
            });
        }
    }

    // Map methods
    private setupMap(): void {
        const trip = this.trip();
        const order = this.order();
        
        if (!trip || !order || !order.pickupPoint) return;

        const nodes: Layer[] = [];
        
        // Add pickup location marker with person icon
        const pickupMarker = marker([order.pickupPoint.latitude, order.pickupPoint.longitude], {
            icon: icon({
                iconUrl: 'https://cdn-icons-png.flaticon.com/512/1077/1077114.png', // Person icon
                iconSize: [32, 32],
                iconAnchor: [16, 32],
                popupAnchor: [0, -32],
                shadowSize: [0, 0]
            })
        });
        nodes.push(pickupMarker);

        // Add driver location marker with car icon if available
        if (trip.currentLocationLatitude && trip.currentLocationLongitude) {
            const driverMarker = marker([trip.currentLocationLatitude, trip.currentLocationLongitude], {
                icon: icon({
                    iconUrl: 'https://cdn-icons-png.flaticon.com/512/744/744465.png', // Car icon
                    iconSize: [32, 32],
                    iconAnchor: [16, 32],
                    popupAnchor: [0, -32],
                    shadowSize: [0, 0]
                })
            });
            nodes.push(driverMarker);
            
            // Center map on driver location
            this.mapCenter.set(latLng(trip.currentLocationLatitude, trip.currentLocationLongitude));
        } else {
            // Center map on pickup location if no driver location
            this.mapCenter.set(latLng(order.pickupPoint.latitude, order.pickupPoint.longitude));
        }

        this.mapNodes.set(nodes);
    }

    // Swipe functionality
    isSliding = signal<boolean>(false);
    isSwipeCompleted = signal<boolean>(false);
    private startX = 0;
    private currentX = 0;
    private swipeThreshold = 50; // Minimum distance for a swipe

    startSlide(event: MouseEvent | TouchEvent): void {
        if (this.isActionInProgress()) return;
        
        this.isSliding.set(true);
        this.isSwipeCompleted.set(false);
        this.startX = event instanceof MouseEvent ? event.clientX : event.touches[0].clientX;
        this.currentX = this.startX;
        
        // Prevent default to avoid text selection
        event.preventDefault();
    }

    onSlide(event: MouseEvent | TouchEvent): void {
        if (!this.isSliding()) return;

        const currentX = event instanceof MouseEvent ? event.clientX : event.touches[0].clientX;
        const deltaX = currentX - this.startX;

        // Only allow rightward swiping
        if (deltaX > 0) {
            this.currentX = Math.min(currentX, this.startX + this.swipeThreshold);
        }
        
        // Prevent default to avoid text selection
        event.preventDefault();
    }

    endSlide(event: MouseEvent | TouchEvent): void {
        if (!this.isSliding()) return;

        const currentX = event instanceof MouseEvent ? event.clientX : event.changedTouches[0].clientX;
        const deltaX = currentX - this.startX;

        if (deltaX >= this.swipeThreshold) {
            this.isSwipeCompleted.set(true);
            
            // Handle different actions based on trip status
            const trip = this.trip();
            if (trip?.status === TripStatus.DRIVER_DIDNT_ARRIVE) {
                this.markArrived();
            } else if (trip?.status === TripStatus.DRIVER_WAITING_CLIENT) {
                this.markClientPacked();
            }
        } else {
            this.isSwipeCompleted.set(false);
            this.currentX = this.startX; // Reset to start position
        }
        this.isSliding.set(false);
    }

    getSwipeProgress(): number {
        if (!this.isSliding() && !this.isSwipeCompleted()) {
            return 0;
        }
        
        if (this.isSwipeCompleted()) {
            return 100;
        }
        
        const progress = (this.currentX - this.startX) / this.swipeThreshold;
        return Math.min(Math.max(progress * 100, 0), 100);
    }

    getHandlePosition(): number {
        if (!this.isSliding() && !this.isSwipeCompleted()) {
            return 0;
        }
        
        if (this.isSwipeCompleted()) {
            return 85; // Leave some space for the handle
        }
        
        const progress = (this.currentX - this.startX) / this.swipeThreshold;
        const clampedProgress = Math.min(Math.max(progress, 0), 1);
        return clampedProgress * 85; // Max 85% to leave space for handle
    }
} 