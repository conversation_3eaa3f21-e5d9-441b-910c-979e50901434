import { Controller, Get, UseGuards } from '@nestjs/common';
import { TransactionService } from './transaction.service';
import { AuthGuard } from '../common/guards';
import { GetUser } from '../common/decorators';
import { MoneyTransaction, User } from '@prisma/client';

@Controller('api/transactions')
@UseGuards(AuthGuard)
export class TransactionController {
  constructor(private transactionService: TransactionService) {}

  @Get('me')
  async getMyTransactions(@GetUser() user: User): Promise<MoneyTransaction[]> {
    return this.transactionService.getUserTransactions(user.id);
  }
} 