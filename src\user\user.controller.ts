import { Body, Controller, Post, Get, UseGuards, Query } from '@nestjs/common';
import { UserService } from './user.service';
import {
  SignUpDto,
  SignInDto,
  RefreshTokenDto,
  VerifyPhoneDto,
  GetUsersDto,
} from './dto';
import { GetUser } from 'src/common/decorators/get-user.decorator';
import { AuthGuard } from 'src/common/guards/auth.guard';
import { User } from '@prisma/client';
import { UltraMsgService } from 'src/common/ultramsg/ultramsg.service';
import { log } from 'console';

@Controller('api/users')
export class UserController {
  constructor(
    private userService: UserService,
    private ultramsgService: UltraMsgService,
  ) {}

  @Post('auth/sign-up')
  signUp(@Body() body: SignUpDto) {
    return this.userService.signUp(body);
  }

  @Post('auth/sign-in')
  signIn(@Body() body: SignInDto) {
    return this.userService.logIn(body);
  }

  @Post('auth/verify-phone')
  verifyPhone(@Body() body: VerifyPhoneDto) {
    return this.userService.verifyPhone(body.phone_number, body.code);
  }

  @Post('auth/send-verification-code')
  sendVerificationCode(@Body('phone_number') phoneNumber: string) {
    return this.ultramsgService.sendVerificationCode(phoneNumber);
  }

  @Post('auth/refresh')
  refresh(@Body() body: RefreshTokenDto) {
    return this.userService.refresh(body);
  }

  @UseGuards(AuthGuard)
  @Get('me')
  getProfile(@GetUser('id') userId: string) {
    return this.userService.getUserById(userId);
  }

  @Get()
  @UseGuards(AuthGuard)
  async findAll(@Query() query: GetUsersDto) {
    return this.userService.getUsers(query.driverStatus);
  }
}
