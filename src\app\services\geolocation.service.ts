import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { firstValueFrom, from, Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { Geolocation } from '@capacitor/geolocation';
import { toObservable } from '@angular/core/rxjs-interop';

@Injectable({
    providedIn: 'root',
})
export class GeolocationService {
    api_key = '2d1b59a2a9be43a60d666027dd1cee447839b5dea56b0876143f9fe3';
    constructor(private http: HttpClient) {}

    getUserCountry() {
        return from(Geolocation.getCurrentPosition()).pipe(
            map((position) => {
                return {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude,
                };
            }),
        );
    }
}
