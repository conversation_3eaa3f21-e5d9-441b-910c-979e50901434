import { Component, computed, inject, signal } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { divIcon, latLng, LatLng, marker } from 'leaflet';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ButtonDirective } from 'primeng/button';
import { Card } from 'primeng/card';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { PopoverModule } from 'primeng/popover';
import { ToastModule } from 'primeng/toast';
import { StopPoint, StopPointService } from '../../services';
import { MapComponent } from '../../shared/components/map.component';
import { injectMany } from '../../shared/helpers/injectMany';
import { getRandomColor } from '../../shared/helpers/randomColor';

@Component({
    selector: 'app-stop-points',
    imports: [
        MapComponent,
        PopoverModule,
        Card,
        ButtonDirective,
        RouterLink,
        ConfirmDialogModule,
        ToastModule,
    ],
    templateUrl: './stop-points.component.html',
})
export class StopPointsComponent {
    services = injectMany({ StopPointService, Router });
    private confirmationService = inject(ConfirmationService);
    private messageService = inject(MessageService);

    markedPlaces = signal<(StopPoint & { color: string })[]>([]);
    markedPlacesMap = computed(() => {
        return this.markedPlaces().map((v) => {
            return this.addMarkerOnClick(
                latLng(v.latitude, v.longitude),
                v.name,
                v.color || getRandomColor(),
            );
        });
    });

    ngOnInit(): void {
        this.getMarkedPlaces();
    }

    getMarkedPlaces() {
        this.services.StopPointService.getAllStopPoints().subscribe((val) => {
            if (val.data) {
                const d = val.data.map((v) => ({
                    ...v,
                    color: getRandomColor(),
                }));
                this.markedPlaces.set(d);
            }
        });
    }

    addMarkerOnClick(latlng: LatLng, name: string, color: string) {
        const newMarker = marker(latlng, {
            draggable: false,
            icon: divIcon({
                iconSize: [36, 36],
                className: '',
                html: `<div class="flex size-10 items-center justify-center rounded-full bg-opacity-30" style="background-color:${color}4d">
                              <div class="flex size-8 items-center justify-center rounded-full text-white" style="background-color:${color}">
                              </div>
                          </div>`,
            }),
        }).bindPopup(() => {
            return `<div>${name}</div>`;
        });
        return newMarker;
    }

    editPlace(place: StopPoint): void {
        this.services.Router.navigate([
            '/main',
            'stop-points',
            'edit',
            place.id,
        ]);
    }

    deletePlace(place: StopPoint): void {
        this.confirmationService.confirm({
            message: `Are you sure you want to delete "${place.name}"?`,
            header: 'Delete Confirmation',
            icon: 'pi pi-exclamation-triangle',
            accept: () => {
                // this.services.StopPointService.deleteStopPoint(
                //     place.id,
                // ).subscribe((response) => {
                //     if (response.data) {
                //         // Remove the deleted place from the list
                //         this.markedPlaces.update((places) =>
                //             places.filter((p) => p.id !== place.id),
                //         );
                //         this.messageService.add({
                //             severity: 'success',
                //             summary: 'Success',
                //             detail: `"${place.name}" has been deleted successfully`,
                //         });
                //     }
                // });
            },
        });
    }
}
