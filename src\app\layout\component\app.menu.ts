import { Component, OnInit, inject, signal } from '@angular/core';

import { RouterModule } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { AppMenuitem } from './app.menuitem';
import { UserService } from '../../services/user.service';
import { DriverStatus } from '../../core/types/tripoos.types';
import { AuthService } from '../../services/auth.service';
import { TranslatePipe } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'app-menu',
    standalone: true,
    imports: [AppMenuitem, RouterModule, CommonModule, TranslatePipe],
    template: `<ul class="layout-menu">
        @for (item of model; track item; let i = $index) {
            @if (!item.separator) {
                <li app-menuitem [item]="item" [index]="i" [root]="true"></li>
            }
            @if (item.separator) {
                <li class="menu-separator"></li>
            }
        }

        <!-- User profile and signout section -->
        <li
            class="layout-menuitem-root-text mt-8 border-t pt-4 text-xl text-text-color-100"
        >
            User Profile
        </li>
        <li class="layout-menuitem layout-menu">
            <a
                [routerLink]="['/main/user-profile']"
                class="flex cursor-pointer items-center gap-4 p-3 text-lg text-text-color-100 transition-colors"
            >
                <i class="pi pi-user mx-2"></i>
                <span>{{ authService.user()?.firstName || 'User' }}</span>
            </a>
        </li>
        <li class="layout-menuitem layout-menu">
            <a
                (click)="logout()"
                class="flex cursor-pointer items-center gap-4 rounded-lg p-3 text-lg text-red-600 transition-colors hover:bg-red-50 dark:hover:bg-red-900 dark:hover:bg-opacity-20"
            >
                <i class="pi pi-sign-out mx-2"></i>
                <span>{{ 'Logout' | translate }}</span>
            </a>
        </li>
    </ul>`,
})
export class AppMenu implements OnInit {
    private userService = inject(UserService);
    authService = inject(AuthService);

    model: MenuItem[] = [];
    isDriver = signal<boolean>(false);

    ngOnInit() {
        this.loadUserProfile();
        this.buildMenu();
    }

    private loadUserProfile() {
        this.userService.getProfile().subscribe({
            next: (response) => {
                if (response.data) {
                    this.isDriver.set(
                        response.data.driverStatus === DriverStatus.APPROVED,
                    );
                    this.buildMenu(); // Rebuild menu after getting user info
                }
            },
            error: (error) => {
                console.error('Error loading user profile:', error);
            },
        });
    }

    private buildMenu() {
        const menuItems: MenuItem[] = [
            {
                label: 'Create Order',
                icon: 'pi pi-fw pi-plus',
                routerLink: ['/main/order'],
            },
            {
                label: 'marked places',
                icon: 'pi pi-fw pi-map-marker',
                routerLink: ['/main/marked-places'],
            },
            {
                label: 'Stop Points',
                icon: 'pi pi-fw pi-map-marker',
                routerLink: ['/main/stop-points'],
            },
            {
                label: 'trips history',
                icon: 'pi pi-fw pi-list-check',
                routerLink: ['/main/trips-history'],
            },
            {
                label: 'Transactions',
                icon: 'pi pi-fw pi-credit-card',
                routerLink: ['/main/transactions'],
            },
        ];

        // Add driver-only menu items
        if (this.isDriver()) {
            menuItems.splice(4, 0, {
                label: 'Find Orders',
                icon: 'pi pi-search',
                routerLink: ['/main/find-orders'],
            });
        }

        this.model = [
            {
                items: menuItems,
            },
        ];
    }

    logout() {
        this.authService.logOut();
    }
}
