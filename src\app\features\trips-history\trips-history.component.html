<div class="mt-10">
    <div class="grid">
        <div class="col-12">
            <h2 class="mb-4">Trip History</h2>

            <!-- Loading state -->

            @if (orders.length === 0) {
                <div
                    class="justify-content-center align-items-center flex"
                    style="min-height: 200px"
                >
                    <div class="text-center">
                        <i
                            class="pi pi-calendar-times mb-3 text-5xl text-gray-400"
                        ></i>
                        <p>You don't have any trips yet.</p>
                    </div>
                </div>
            }

            <!-- Trip cards -->
            @else {
                <div class="grid">
                    @for (order of orders; track order.id) {
                        <div class="col-12 md:col-6 lg:col-4 mb-3">
                            <p-card>
                                <ng-template pTemplate="header">
                                    <div class="bg-primary p-3 text-white">
                                        <div
                                            class="justify-content-between align-items-center flex"
                                        >
                                            <h3 class="m-0">
                                                Trip #{{
                                                    order.id.substring(0, 8)
                                                }}
                                            </h3>
                                            <span
                                                class="rounded-lg px-2 py-1 text-sm font-medium {{
                                                    getStatusClass(order.status)
                                                }}"
                                            >
                                                {{ order.status }}
                                            </span>
                                        </div>
                                    </div>
                                </ng-template>

                                <div class="mb-3">
                                    <div class="align-items-center mb-2 flex">
                                        <i class="pi pi-calendar mr-2"></i>
                                        <span>{{
                                            formatDate(order.createdAt)
                                        }}</span>
                                    </div>

                                    <div class="align-items-center mb-2 flex">
                                        <i
                                            class="pi pi-map-marker mr-2 text-green-500"
                                        ></i>
                                        <span class="font-semibold">From:</span>
                                        @if (order.pickupPointId) {
                                            <span class="ml-2"
                                                >Point ID:
                                                {{ order.pickupPointId }}</span
                                            >
                                        } @else {
                                            <span class="ml-2"
                                                >Unknown location</span
                                            >
                                        }
                                    </div>

                                    <div class="align-items-center mb-2 flex">
                                        <i
                                            class="pi pi-flag-fill mr-2 text-red-500"
                                        ></i>
                                        <span class="font-semibold">To:</span>
                                        @if (order.dropoffPointId) {
                                            <span class="ml-2"
                                                >Point ID:
                                                {{ order.dropoffPointId }}</span
                                            >
                                        } @else {
                                            <span class="ml-2"
                                                >Unknown location</span
                                            >
                                        }
                                    </div>

                                    @if (order.tripId) {
                                        <div class="align-items-center flex">
                                            <i
                                                class="pi pi-car mr-2 text-blue-500"
                                            ></i>
                                            <span class="font-semibold"
                                                >Trip ID:</span
                                            >
                                            <span class="ml-2">{{
                                                order.tripId.substring(0, 8)
                                            }}</span>
                                        </div>
                                    }
                                </div>

                                <div class="justify-content-center flex gap-2">
                                    <button
                                        pButton
                                        icon="pi pi-map"
                                        label="View Details"
                                        class="p-button-outlined"
                                        (click)="viewTripDetails(order)"
                                    ></button>

                                    @if (
                                        order.status === "PENDING" ||
                                        order.status === "SUGGESTED_FOR_DRIVER"
                                    ) {
                                        <button
                                            pButton
                                            icon="pi pi-check"
                                            label="Approve"
                                            class="p-button-outlined p-button-success"
                                            (click)="approve(order)"
                                        ></button>
                                    }

                                    <!-- Cancel button removed as the cancelOrder API is not implemented -->
                                </div>
                            </p-card>
                        </div>
                    }
                </div>
            }
        </div>
    </div>
</div>
